# Playwright MCP 连接问题修复记录

## 问题描述

在使用 `run_web_api.py` 启动 Web API 服务时，Playwright MCP 无法正常连接，出现以下错误：

```
2025-07-21 16:17:08.655 | ERROR    | app.agent.manus:initialize_mcp_servers:91 - Failed to connect to MCP server playwright: 
```

而使用 `run_with_playwright_mcp.py` 启动时，Playwright MCP 能够正常工作。

## 问题分析

### 症状对比

1. **`run_with_playwright_mcp.py`** - ✅ 正常工作
   - 直接调用 `Manus.create()`
   - 成功连接 Playwright MCP
   - 发现 25 个 Playwright 工具

2. **`run_web_api.py`** - ❌ 连接失败
   - 通过 Web API 后台任务初始化
   - Playwright MCP 连接失败，抛出 `NotImplementedError`
   - 只连接了 amap-maps-sse，未连接 playwright

### 根本原因

通过详细测试发现，问题出现在 **uvicorn 的 reload 模式**：

1. **Web API 配置**：`debug: bool = True` 导致 `reload=True`
2. **Reload 模式影响**：uvicorn 在 reload 模式下会创建子进程运行应用
3. **事件循环冲突**：子进程的事件循环与 MCP stdio 连接产生冲突
4. **NotImplementedError**：MCP 客户端在特定事件循环环境下无法正常工作

### 验证过程

1. **独立测试** - ✅ 成功
   ```bash
   python test_playwright_mcp.py
   ```
   - 直接运行时 Playwright MCP 连接正常

2. **简化 uvicorn 测试** - ✅ 成功
   ```python
   # 使用简单的 uvicorn + FastAPI，无 reload
   uvicorn.run(app, host="127.0.0.1", port=8001)
   ```
   - 不使用 reload 模式时连接正常

3. **原始 Web API** - ❌ 失败
   ```python
   # 使用 reload=config.debug (True)
   uvicorn.run("web_api.main:app", reload=True)
   ```
   - reload 模式下连接失败

## 解决方案

### 修改配置文件

**文件**: `web_api/config.py`

```python
# 修改前
debug: bool = True

# 修改后  
debug: bool = False  # 禁用reload模式以修复MCP连接问题
```

### 修复效果

修改后重新启动 Web API：

```
2025-07-21 16:54:05.776 | INFO | web_api.services.background:initialize_manus_with_mcp:85 - ✅ 成功连接到 2 个 MCP 服务器！
2025-07-21 16:54:05.776 | INFO | web_api.services.background:initialize_manus_with_mcp:86 - 📋 发现 40 个工具，其中 25 个 Playwright/浏览器工具
2025-07-21 16:54:05.776 | INFO | web_api.services.background:initialize_manus_with_mcp:89 - 🎭 Playwright MCP 集成成功，可用工具包括:
```

**结果**：
- ✅ 成功连接 2 个 MCP 服务器（playwright + amap-maps-sse）
- ✅ 发现 40 个工具（25 个 Playwright + 15 个地图工具）
- ✅ Playwright MCP 完全正常工作

## 技术细节

### MCP 连接配置

**文件**: `config/mcp.json`

```json
{
  "mcpServers": {
    "playwright": {
      "type": "stdio",
      "command": "node",
      "args": [
        "external/playwright-mcp/cli.js",
        "--browser=chrome",
        "--no-sandbox",
        "--config=playwright-mcp-config.json"
      ]
    },
    "amap-maps-sse": {
      "type": "sse",
      "url": "https://mcp.amap.com/sse?key=28410b74a8c0365773e87bd2c52b98b6"
    }
  }
}
```

### 可用的 Playwright 工具

修复后，Web API 现在可以使用以下 Playwright 工具：

- `mcp_playwright_browser_navigate` - 导航到网页
- `mcp_playwright_browser_take_screenshot` - 截图
- `mcp_playwright_browser_click` - 点击元素
- `mcp_playwright_browser_type` - 输入文本
- `mcp_playwright_browser_tab_new` - 新建标签页
- 等等... (总共 25 个工具)

## 影响和注意事项

### 开发体验

- **优点**：MCP 连接稳定，功能完整
- **缺点**：失去了代码热重载功能

### 替代方案

如果需要开发时的热重载功能，可以：

1. **开发时**：临时设置 `debug: bool = True`，但 Playwright MCP 不可用
2. **测试时**：设置 `debug: bool = False`，完整功能可用
3. **生产时**：始终使用 `debug: bool = False`

### 长期解决方案

未来可以考虑：
1. 研究 MCP 客户端在 reload 模式下的兼容性
2. 实现条件性 MCP 初始化（仅在非 reload 模式下）
3. 使用外部 MCP 服务器而非 stdio 连接

## 总结

通过禁用 uvicorn 的 reload 模式，成功解决了 Playwright MCP 连接问题。现在 Web API 服务可以完整使用 Playwright 的所有浏览器自动化功能，包括导航、截图、点击、输入等操作。

这个修复确保了 `run_web_api.py` 和 `run_with_playwright_mcp.py` 具有相同的 MCP 功能，为用户提供了一致的体验。
