"""
Web API 主应用模块（简化版）
"""

import asyncio
from contextlib import asynccontextmanager

from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware

from .config import config
from .routers import agent_detail, mcp_router, sessions, tasks, tools, websocket
from .services.background import start_background_tasks
from .services.state_manager import state_manager


@asynccontextmanager
async def lifespan(app: FastAPI):
    """应用生命周期管理"""
    # 启动时执行
    print("🚀 启动后台任务...")
    try:
        await start_background_tasks()
        print("✅ 后台任务启动完成")
    except Exception as e:
        print(f"⚠️ 后台任务启动失败: {e}")

    yield

    # 关闭时执行
    print("🧹 清理资源...")
    try:
        from .services.background import get_global_manus_instance

        global_manus = get_global_manus_instance()
        if global_manus:
            try:
                # 使用 asyncio.wait_for 设置超时，避免无限等待
                await asyncio.wait_for(global_manus.cleanup(), timeout=10.0)
                print("✅ Manus 实例清理完成")
            except asyncio.TimeoutError:
                print("⚠️ Manus 清理超时，强制退出")
            except Exception as e:
                print(f"⚠️ Manus 清理时出错: {e}")
    except Exception as e:
        print(f"⚠️ 资源清理过程中出错: {e}")
    finally:
        print("✅ 清理完成")


def create_app() -> FastAPI:
    """创建 FastAPI 应用实例"""
    app = FastAPI(
        title=config.title,
        description=config.description,
        version=config.version,
        openapi_url=config.openapi_url,
        docs_url=config.docs_url,
        redoc_url=config.redoc_url,
        lifespan=lifespan,
    )

    # CORS配置（开发用）
    app.add_middleware(
        CORSMiddleware,
        allow_origins=["*"],
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )

    # 路由注册
    app.include_router(sessions.router, prefix="/api")
    app.include_router(tasks.router, prefix="/api")
    app.include_router(tools.router, prefix="/api")
    app.include_router(agent_detail.router, prefix="/api")
    app.include_router(mcp_router.router)  # MCP路由已经包含了/api/mcp前缀
    app.include_router(websocket.router, prefix="/ws")

    @app.get("/")
    async def root():
        """根路径"""
        return {
            "message": "RPA Agent 简化版 API",
            "version": config.version,
            "docs": "/docs",
            "websocket": "/ws/sessions/{session_id}",
            "status": "running",
        }

    @app.get("/api/health")
    async def health_check():
        """服务健康状态"""
        stats = state_manager.get_stats()
        return {
            "status": "ok",
            "message": "服务运行正常",
            "version": config.version,
            "sessions": stats["sessions"],
            "tasks": stats["tasks"],
            "active_tasks": stats["active_tasks"],
            "websocket_connections": stats["websocket_connections"],
        }

    return app


# 创建应用实例
app = create_app()


if __name__ == "__main__":
    import uvicorn

    uvicorn.run(app, host=config.host, port=config.port)
