"""File operation interfaces and implementations for local and sandbox environments."""

import async<PERSON>
import json
from pathlib import Path
from typing import Any, Dict, List, Optional, Protocol, Tuple, Union, runtime_checkable

from app.config import SandboxSettings
from app.exceptions import ToolError
from app.sandbox.client import SANDBOX_CLIENT

# Excel processing imports (lazy loading to avoid import errors if not installed)
try:
    import openpyxl
    import pandas as pd
    from openpyxl.styles import Alignment, Border, Font, PatternFill, Side

    EXCEL_AVAILABLE = True
except ImportError:
    EXCEL_AVAILABLE = False


PathLike = Union[str, Path]


@runtime_checkable
class FileOperator(Protocol):
    """Interface for file operations in different environments."""

    async def read_file(self, path: PathLike) -> str:
        """Read content from a file."""
        ...

    async def write_file(self, path: PathLike, content: str) -> None:
        """Write content to a file."""
        ...

    async def is_directory(self, path: PathLike) -> bool:
        """Check if path points to a directory."""
        ...

    async def exists(self, path: PathLike) -> bool:
        """Check if path exists."""
        ...

    async def run_command(
        self, cmd: str, timeout: Optional[float] = 120.0
    ) -> Tuple[int, str, str]:
        """Run a shell command and return (return_code, stdout, stderr)."""
        ...

    async def read_excel_file(self, path: PathLike, **kwargs) -> Dict[str, Any]:
        """Read Excel file and return structured data."""
        ...

    async def write_excel_file(
        self, path: PathLike, data: Dict[str, Any], **kwargs
    ) -> None:
        """Write structured data to Excel file."""
        ...


class LocalFileOperator(FileOperator):
    """File operations implementation for local filesystem."""

    encoding: str = "utf-8"

    async def read_file(self, path: PathLike) -> str:
        """Read content from a local file."""
        try:
            return Path(path).read_text(encoding=self.encoding)
        except Exception as e:
            raise ToolError(f"Failed to read {path}: {str(e)}") from None

    async def write_file(self, path: PathLike, content: str) -> None:
        """Write content to a local file."""
        try:
            Path(path).write_text(content, encoding=self.encoding)
        except Exception as e:
            raise ToolError(f"Failed to write to {path}: {str(e)}") from None

    async def is_directory(self, path: PathLike) -> bool:
        """Check if path points to a directory."""
        return Path(path).is_dir()

    async def exists(self, path: PathLike) -> bool:
        """Check if path exists."""
        return Path(path).exists()

    async def run_command(
        self, cmd: str, timeout: Optional[float] = 120.0
    ) -> Tuple[int, str, str]:
        """Run a shell command locally."""
        process = await asyncio.create_subprocess_shell(
            cmd, stdout=asyncio.subprocess.PIPE, stderr=asyncio.subprocess.PIPE
        )

        try:
            stdout, stderr = await asyncio.wait_for(
                process.communicate(), timeout=timeout
            )
            return (
                process.returncode or 0,
                stdout.decode(),
                stderr.decode(),
            )
        except asyncio.TimeoutError as exc:
            try:
                process.kill()
            except ProcessLookupError:
                pass
            raise TimeoutError(
                f"Command '{cmd}' timed out after {timeout} seconds"
            ) from exc

    async def read_excel_file(self, path: PathLike, **kwargs) -> Dict[str, Any]:
        """Read Excel file and return structured data."""
        if not EXCEL_AVAILABLE:
            raise ToolError(
                "Excel processing libraries not available. Please install pandas and openpyxl."
            )

        try:
            file_path = Path(path)
            if not file_path.exists():
                raise ToolError(f"Excel file not found: {path}")

            # Default parameters
            sheet_name = kwargs.get("sheet_name", None)  # None means all sheets
            header = kwargs.get("header", 0)
            skiprows = kwargs.get("skiprows", None)
            nrows = kwargs.get("nrows", None)
            usecols = kwargs.get("usecols", None)

            # Read Excel file
            if sheet_name is None:
                # Read all sheets
                excel_data = pd.read_excel(
                    file_path,
                    sheet_name=None,
                    header=header,
                    skiprows=skiprows,
                    nrows=nrows,
                    usecols=usecols,
                    engine="openpyxl",
                )
                result = {}
                for sheet, df in excel_data.items():
                    result[sheet] = {
                        "data": df.fillna("").to_dict("records"),
                        "columns": df.columns.tolist(),
                        "shape": df.shape,
                    }
            else:
                # Read specific sheet
                df = pd.read_excel(
                    file_path,
                    sheet_name=sheet_name,
                    header=header,
                    skiprows=skiprows,
                    nrows=nrows,
                    usecols=usecols,
                    engine="openpyxl",
                )
                result = {
                    sheet_name: {
                        "data": df.fillna("").to_dict("records"),
                        "columns": df.columns.tolist(),
                        "shape": df.shape,
                    }
                }

            return {
                "success": True,
                "file_path": str(file_path),
                "sheets": result,
                "total_sheets": len(result),
            }

        except Exception as e:
            raise ToolError(f"Failed to read Excel file {path}: {str(e)}") from None

    async def write_excel_file(
        self, path: PathLike, data: Dict[str, Any], **kwargs
    ) -> None:
        """Write structured data to Excel file."""
        if not EXCEL_AVAILABLE:
            raise ToolError(
                "Excel processing libraries not available. Please install pandas and openpyxl."
            )

        try:
            file_path = Path(path)
            file_path.parent.mkdir(parents=True, exist_ok=True)

            # Extract parameters
            engine = kwargs.get("engine", "openpyxl")
            index = kwargs.get("index", False)
            header = kwargs.get("header", True)

            # Prepare data for writing
            sheets_data = data.get("sheets", {})
            if not sheets_data:
                raise ToolError("No sheet data provided for Excel file")

            with pd.ExcelWriter(file_path, engine=engine) as writer:
                for sheet_name, sheet_data in sheets_data.items():
                    if isinstance(sheet_data, dict) and "data" in sheet_data:
                        # Data is in structured format
                        df = pd.DataFrame(sheet_data["data"])
                    elif isinstance(sheet_data, list):
                        # Data is a list of records
                        df = pd.DataFrame(sheet_data)
                    else:
                        # Assume it's already a DataFrame or convertible
                        df = pd.DataFrame(sheet_data)

                    # Write to Excel
                    df.to_excel(
                        writer, sheet_name=sheet_name, index=index, header=header
                    )

                    # Apply formatting if specified
                    if "formatting" in kwargs:
                        self._apply_excel_formatting(
                            writer, sheet_name, kwargs["formatting"]
                        )

        except Exception as e:
            raise ToolError(f"Failed to write Excel file {path}: {str(e)}") from None

    def _apply_excel_formatting(
        self, writer, sheet_name: str, formatting: Dict[str, Any]
    ) -> None:
        """Apply formatting to Excel sheet."""
        try:
            workbook = writer.book
            worksheet = writer.sheets[sheet_name]

            # Apply header formatting
            if "header" in formatting:
                header_format = formatting["header"]
                for col_num, cell in enumerate(worksheet[1], 1):
                    if "font" in header_format:
                        cell.font = Font(**header_format["font"])
                    if "fill" in header_format:
                        cell.fill = PatternFill(**header_format["fill"])
                    if "alignment" in header_format:
                        cell.alignment = Alignment(**header_format["alignment"])

            # Apply column widths
            if "column_widths" in formatting:
                for col, width in formatting["column_widths"].items():
                    worksheet.column_dimensions[col].width = width

            # Apply borders
            if "borders" in formatting:
                border_style = Border(**formatting["borders"])
                for row in worksheet.iter_rows():
                    for cell in row:
                        cell.border = border_style

        except Exception as e:
            # Don't fail the entire operation for formatting errors
            pass


class SandboxFileOperator(FileOperator):
    """File operations implementation for sandbox environment."""

    def __init__(self):
        self.sandbox_client = SANDBOX_CLIENT

    async def _ensure_sandbox_initialized(self):
        """Ensure sandbox is initialized."""
        if not self.sandbox_client.sandbox:
            await self.sandbox_client.create(config=SandboxSettings())

    async def read_file(self, path: PathLike) -> str:
        """Read content from a file in sandbox."""
        await self._ensure_sandbox_initialized()
        try:
            return await self.sandbox_client.read_file(str(path))
        except Exception as e:
            raise ToolError(f"Failed to read {path} in sandbox: {str(e)}") from None

    async def write_file(self, path: PathLike, content: str) -> None:
        """Write content to a file in sandbox."""
        await self._ensure_sandbox_initialized()
        try:
            await self.sandbox_client.write_file(str(path), content)
        except Exception as e:
            raise ToolError(f"Failed to write to {path} in sandbox: {str(e)}") from None

    async def is_directory(self, path: PathLike) -> bool:
        """Check if path points to a directory in sandbox."""
        await self._ensure_sandbox_initialized()
        result = await self.sandbox_client.run_command(
            f"test -d {path} && echo 'true' || echo 'false'"
        )
        return result.strip() == "true"

    async def exists(self, path: PathLike) -> bool:
        """Check if path exists in sandbox."""
        await self._ensure_sandbox_initialized()
        result = await self.sandbox_client.run_command(
            f"test -e {path} && echo 'true' || echo 'false'"
        )
        return result.strip() == "true"

    async def run_command(
        self, cmd: str, timeout: Optional[float] = 120.0
    ) -> Tuple[int, str, str]:
        """Run a command in sandbox environment."""
        await self._ensure_sandbox_initialized()
        try:
            stdout = await self.sandbox_client.run_command(
                cmd, timeout=int(timeout) if timeout else None
            )
            return (
                0,  # Always return 0 since we don't have explicit return code from sandbox
                stdout,
                "",  # No stderr capture in the current sandbox implementation
            )
        except TimeoutError as exc:
            raise TimeoutError(
                f"Command '{cmd}' timed out after {timeout} seconds in sandbox"
            ) from exc
        except Exception as exc:
            return 1, "", f"Error executing command in sandbox: {str(exc)}"

    async def read_excel_file(self, path: PathLike, **kwargs) -> Dict[str, Any]:
        """Read Excel file in sandbox environment."""
        if not EXCEL_AVAILABLE:
            raise ToolError(
                "Excel processing libraries not available. Please install pandas and openpyxl."
            )

        await self._ensure_sandbox_initialized()
        try:
            # First check if file exists
            if not await self.exists(path):
                raise ToolError(f"Excel file not found in sandbox: {path}")

            # Create a temporary Python script to read Excel in sandbox
            script_content = f"""
import pandas as pd
import json
import sys

try:
    # Read Excel file with parameters
    sheet_name = {repr(kwargs.get('sheet_name', None))}
    header = {kwargs.get('header', 0)}
    skiprows = {repr(kwargs.get('skiprows', None))}
    nrows = {repr(kwargs.get('nrows', None))}
    usecols = {repr(kwargs.get('usecols', None))}

    if sheet_name is None:
        # Read all sheets
        excel_data = pd.read_excel(
            "{path}",
            sheet_name=None,
            header=header,
            skiprows=skiprows,
            nrows=nrows,
            usecols=usecols,
            engine="openpyxl"
        )
        result = {{}}
        for sheet, df in excel_data.items():
            result[sheet] = {{
                "data": df.fillna("").to_dict("records"),
                "columns": df.columns.tolist(),
                "shape": df.shape
            }}
    else:
        # Read specific sheet
        df = pd.read_excel(
            "{path}",
            sheet_name=sheet_name,
            header=header,
            skiprows=skiprows,
            nrows=nrows,
            usecols=usecols,
            engine="openpyxl"
        )
        result = {{
            sheet_name: {{
                "data": df.fillna("").to_dict("records"),
                "columns": df.columns.tolist(),
                "shape": df.shape
            }}
        }}

    output = {{
        "success": True,
        "file_path": "{path}",
        "sheets": result,
        "total_sheets": len(result)
    }}

    print(json.dumps(output, ensure_ascii=False))

except Exception as e:
    error_output = {{
        "success": False,
        "error": str(e)
    }}
    print(json.dumps(error_output, ensure_ascii=False))
    sys.exit(1)
"""

            # Write script to sandbox
            script_path = "/tmp/read_excel_script.py"
            await self.write_file(script_path, script_content)

            # Execute script in sandbox
            stdout = await self.sandbox_client.run_command(f"python {script_path}")

            # Parse result
            result = json.loads(stdout)
            if not result.get("success", False):
                raise ToolError(
                    f"Failed to read Excel in sandbox: {result.get('error', 'Unknown error')}"
                )

            return result

        except json.JSONDecodeError as e:
            raise ToolError(
                f"Failed to parse Excel read result from sandbox: {str(e)}"
            ) from None
        except Exception as e:
            raise ToolError(
                f"Failed to read Excel file {path} in sandbox: {str(e)}"
            ) from None

    async def write_excel_file(
        self, path: PathLike, data: Dict[str, Any], **kwargs
    ) -> None:
        """Write structured data to Excel file in sandbox."""
        if not EXCEL_AVAILABLE:
            raise ToolError(
                "Excel processing libraries not available. Please install pandas and openpyxl."
            )

        await self._ensure_sandbox_initialized()
        try:
            # Prepare data for sandbox
            sheets_data = data.get("sheets", {})
            if not sheets_data:
                raise ToolError("No sheet data provided for Excel file")

            # Extract parameters
            engine = kwargs.get("engine", "openpyxl")
            index = kwargs.get("index", False)
            header = kwargs.get("header", True)

            # Create a temporary Python script to write Excel in sandbox
            script_content = f"""
import pandas as pd
import json
import sys
from pathlib import Path

try:
    # Data to write
    sheets_data = {json.dumps(sheets_data, ensure_ascii=False)}

    # Create directory if needed
    file_path = Path("{path}")
    file_path.parent.mkdir(parents=True, exist_ok=True)

    # Write Excel file
    with pd.ExcelWriter(file_path, engine="{engine}") as writer:
        for sheet_name, sheet_data in sheets_data.items():
            if isinstance(sheet_data, dict) and "data" in sheet_data:
                df = pd.DataFrame(sheet_data["data"])
            elif isinstance(sheet_data, list):
                df = pd.DataFrame(sheet_data)
            else:
                df = pd.DataFrame(sheet_data)

            df.to_excel(writer, sheet_name=sheet_name, index={index}, header={header})

    print("Excel file written successfully")

except Exception as e:
    print(f"Error writing Excel file: {{str(e)}}")
    sys.exit(1)
"""

            # Write script to sandbox
            script_path = "/tmp/write_excel_script.py"
            await self.write_file(script_path, script_content)

            # Execute script in sandbox
            stdout = await self.sandbox_client.run_command(f"python {script_path}")

            if "Error writing Excel file" in stdout:
                raise ToolError(f"Failed to write Excel in sandbox: {stdout}")

        except Exception as e:
            raise ToolError(
                f"Failed to write Excel file {path} in sandbox: {str(e)}"
            ) from None
