<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Agent执行过程实时流式传输测试</title>
    <script src="https://cdn.jsdelivr.net/npm/marked/marked.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/prismjs@1.29.0/components/prism-core.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/prismjs@1.29.0/plugins/autoloader/prism-autoloader.min.js"></script>
    <link href="https://cdn.jsdelivr.net/npm/prismjs@1.29.0/themes/prism-tomorrow.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystem<PERSON>ont, '<PERSON><PERSON>e UI', <PERSON><PERSON>, sans-serif;
            background: #f5f7fa;
            color: #333;
            line-height: 1.6;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }

        .header h1 {
            color: #2c3e50;
            margin-bottom: 10px;
        }

        .controls {
            display: flex;
            gap: 10px;
            align-items: center;
            flex-wrap: wrap;
        }

        .input-group {
            display: flex;
            gap: 10px;
            align-items: center;
        }

        input, button {
            padding: 8px 16px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 14px;
        }

        button {
            background: #3498db;
            color: white;
            border: none;
            cursor: pointer;
            transition: background 0.3s;
        }

        button:hover {
            background: #2980b9;
        }

        button:disabled {
            background: #bdc3c7;
            cursor: not-allowed;
        }

        .status {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-left: auto;
        }

        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: #e74c3c;
        }

        .status-indicator.connected {
            background: #27ae60;
        }

        .main-content {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            height: calc(100vh - 200px);
        }

        .panel {
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
            display: flex;
            flex-direction: column;
        }

        .panel-header {
            background: #34495e;
            color: white;
            padding: 15px 20px;
            font-weight: 600;
        }

        .panel-content {
            flex: 1;
            overflow-y: auto;
            padding: 20px;
        }

        .markdown-content {
            line-height: 1.8;
        }

        .markdown-content h1, .markdown-content h2, .markdown-content h3 {
            margin-top: 20px;
            margin-bottom: 10px;
            color: #2c3e50;
        }

        .markdown-content h1 {
            border-bottom: 2px solid #3498db;
            padding-bottom: 10px;
        }

        .markdown-content h2 {
            border-bottom: 1px solid #ecf0f1;
            padding-bottom: 5px;
        }

        .markdown-content pre {
            background: #2d3748;
            color: #e2e8f0;
            padding: 15px;
            border-radius: 5px;
            overflow-x: auto;
            margin: 10px 0;
        }

        .markdown-content code {
            background: #f8f9fa;
            padding: 2px 6px;
            border-radius: 3px;
            font-family: 'Monaco', 'Consolas', monospace;
        }

        .markdown-content blockquote {
            border-left: 4px solid #3498db;
            padding-left: 15px;
            margin: 15px 0;
            color: #7f8c8d;
            font-style: italic;
        }

        .event-log {
            font-family: 'Monaco', 'Consolas', monospace;
            font-size: 12px;
            background: #2d3748;
            color: #e2e8f0;
            padding: 15px;
            height: 100%;
            overflow-y: auto;
        }

        .event-item {
            margin-bottom: 10px;
            padding: 8px;
            border-radius: 4px;
            border-left: 3px solid #3498db;
            background: rgba(52, 152, 219, 0.1);
        }

        .event-item.thinking {
            border-left-color: #f39c12;
            background: rgba(243, 156, 18, 0.1);
        }

        .event-item.tool {
            border-left-color: #27ae60;
            background: rgba(39, 174, 96, 0.1);
        }

        .event-item.error {
            border-left-color: #e74c3c;
            background: rgba(231, 76, 60, 0.1);
        }

        .event-timestamp {
            color: #95a5a6;
            font-size: 11px;
        }

        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #3498db;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .auto-scroll {
            scroll-behavior: smooth;
        }

        @media (max-width: 768px) {
            .main-content {
                grid-template-columns: 1fr;
                height: auto;
            }
            
            .panel {
                height: 400px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🤖 Agent执行过程实时流式传输测试</h1>
            <div class="controls">
                <div class="input-group">
                    <label>会话ID:</label>
                    <input type="text" id="sessionId" placeholder="输入会话ID" value="test-session-001">
                </div>
                <div class="input-group">
                    <label>WebSocket URL:</label>
                    <input type="text" id="wsUrl" placeholder="WebSocket地址" value="ws://localhost:8000/ws/sessions/">
                </div>
                <button id="connectBtn">连接</button>
                <button id="disconnectBtn" disabled>断开</button>
                <button id="clearBtn">清空</button>
                <button id="getHistoryBtn" disabled>获取历史</button>
                <div class="status">
                    <span>连接状态:</span>
                    <div class="status-indicator" id="statusIndicator"></div>
                    <span id="statusText">未连接</span>
                </div>
            </div>
        </div>

        <div class="main-content">
            <div class="panel">
                <div class="panel-header">
                    📝 Markdown渲染结果
                    <span id="loadingIndicator" style="display: none; margin-left: 10px;">
                        <div class="loading"></div>
                    </span>
                </div>
                <div class="panel-content">
                    <div id="markdownContent" class="markdown-content auto-scroll">
                        <p><em>等待Agent执行数据...</em></p>
                    </div>
                </div>
            </div>

            <div class="panel">
                <div class="panel-header">
                    🔍 实时事件日志
                    <button id="autoScrollBtn" style="margin-left: auto; padding: 4px 8px; font-size: 12px;">自动滚动: 开</button>
                </div>
                <div class="panel-content" style="padding: 0;">
                    <div id="eventLog" class="event-log auto-scroll"></div>
                </div>
            </div>
        </div>
    </div>

    <script>
        class AgentStreamViewer {
            constructor() {
                this.ws = null;
                this.isConnected = false;
                this.autoScroll = true;
                this.markdownContent = '';
                this.eventCount = 0;
                
                this.initElements();
                this.bindEvents();
            }

            initElements() {
                this.sessionIdInput = document.getElementById('sessionId');
                this.wsUrlInput = document.getElementById('wsUrl');
                this.connectBtn = document.getElementById('connectBtn');
                this.disconnectBtn = document.getElementById('disconnectBtn');
                this.clearBtn = document.getElementById('clearBtn');
                this.getHistoryBtn = document.getElementById('getHistoryBtn');
                this.statusIndicator = document.getElementById('statusIndicator');
                this.statusText = document.getElementById('statusText');
                this.markdownContentDiv = document.getElementById('markdownContent');
                this.eventLogDiv = document.getElementById('eventLog');
                this.loadingIndicator = document.getElementById('loadingIndicator');
                this.autoScrollBtn = document.getElementById('autoScrollBtn');
            }

            bindEvents() {
                this.connectBtn.addEventListener('click', () => this.connect());
                this.disconnectBtn.addEventListener('click', () => this.disconnect());
                this.clearBtn.addEventListener('click', () => this.clearContent());
                this.getHistoryBtn.addEventListener('click', () => this.getHistory());
                this.autoScrollBtn.addEventListener('click', () => this.toggleAutoScroll());
            }

            connect() {
                const sessionId = this.sessionIdInput.value.trim();
                const wsUrl = this.wsUrlInput.value.trim();
                
                if (!sessionId) {
                    alert('请输入会话ID');
                    return;
                }

                const fullUrl = wsUrl + sessionId;
                this.addEventLog('system', `尝试连接到: ${fullUrl}`);

                try {
                    this.ws = new WebSocket(fullUrl);
                    
                    this.ws.onopen = () => {
                        this.isConnected = true;
                        this.updateConnectionStatus(true);
                        this.addEventLog('system', '✅ WebSocket连接成功');
                    };

                    this.ws.onmessage = (event) => {
                        this.handleMessage(JSON.parse(event.data));
                    };

                    this.ws.onclose = (event) => {
                        this.isConnected = false;
                        this.updateConnectionStatus(false);
                        this.addEventLog('system', `❌ WebSocket连接关闭: ${event.reason || '未知原因'}`);
                    };

                    this.ws.onerror = (error) => {
                        this.addEventLog('error', `🚨 WebSocket错误: ${error.message || '连接失败'}`);
                    };

                } catch (error) {
                    this.addEventLog('error', `🚨 连接失败: ${error.message}`);
                }
            }

            disconnect() {
                if (this.ws) {
                    this.ws.close();
                    this.ws = null;
                }
            }

            updateConnectionStatus(connected) {
                this.statusIndicator.className = `status-indicator ${connected ? 'connected' : ''}`;
                this.statusText.textContent = connected ? '已连接' : '未连接';
                this.connectBtn.disabled = connected;
                this.disconnectBtn.disabled = !connected;
                this.getHistoryBtn.disabled = !connected;
            }

            handleMessage(data) {
                this.addEventLog('websocket', `收到消息: ${data.type}`);

                switch (data.type) {
                    case 'agent_event':
                        this.handleAgentEvent(data);
                        break;
                    case 'agent_history':
                        this.handleAgentHistory(data);
                        break;
                    case 'connection':
                        this.addEventLog('system', `🔗 ${data.data.message}`);
                        break;
                    case 'pong':
                        this.addEventLog('system', '🏓 收到心跳响应');
                        break;
                    case 'error':
                        this.addEventLog('error', `❌ 服务器错误: ${data.data.error}`);
                        break;
                    default:
                        this.addEventLog('unknown', `未知消息类型: ${data.type}`);
                }
            }

            handleAgentEvent(data) {
                const eventData = data.data;
                const markdown = eventData.markdown;
                
                // 累积Markdown内容
                this.markdownContent += markdown + '\n\n';
                
                // 渲染Markdown
                this.renderMarkdown();
                
                // 添加事件日志
                this.addEventLog(
                    this.getEventClass(eventData.event_type),
                    `[步骤${eventData.step}] ${eventData.event_type}: ${this.getEventDescription(eventData)}`
                );
            }

            handleAgentHistory(data) {
                const historyData = data.data;
                this.markdownContent = historyData.markdown;
                this.renderMarkdown();
                this.addEventLog('system', `📚 加载历史记录: ${historyData.event_count} 个事件`);
            }

            renderMarkdown() {
                if (this.markdownContent) {
                    this.markdownContentDiv.innerHTML = marked.parse(this.markdownContent);
                    
                    // 代码高亮
                    if (window.Prism) {
                        Prism.highlightAllUnder(this.markdownContentDiv);
                    }
                    
                    if (this.autoScroll) {
                        this.markdownContentDiv.scrollTop = this.markdownContentDiv.scrollHeight;
                    }
                }
            }

            getEventClass(eventType) {
                const classMap = {
                    'agent_thinking': 'thinking',
                    'tool_selection': 'tool',
                    'tool_execution': 'tool',
                    'tool_result': 'tool',
                    'error': 'error'
                };
                return classMap[eventType] || 'default';
            }

            getEventDescription(eventData) {
                const descriptions = {
                    'agent_start': '开始执行',
                    'agent_step': '执行步骤',
                    'agent_thinking': '思考过程',
                    'tool_selection': '选择工具',
                    'tool_execution': '执行工具',
                    'tool_result': '工具结果',
                    'file_operation': '文件操作',
                    'error': '发生错误',
                    'agent_finish': '执行完成'
                };
                return descriptions[eventData.event_type] || eventData.event_type;
            }

            addEventLog(type, message) {
                this.eventCount++;
                const timestamp = new Date().toLocaleTimeString();
                const eventItem = document.createElement('div');
                eventItem.className = `event-item ${type}`;
                eventItem.innerHTML = `
                    <div class="event-timestamp">[${timestamp}] #${this.eventCount}</div>
                    <div>${message}</div>
                `;
                
                this.eventLogDiv.appendChild(eventItem);
                
                if (this.autoScroll) {
                    this.eventLogDiv.scrollTop = this.eventLogDiv.scrollHeight;
                }
            }

            clearContent() {
                this.markdownContent = '';
                this.markdownContentDiv.innerHTML = '<p><em>内容已清空...</em></p>';
                this.eventLogDiv.innerHTML = '';
                this.eventCount = 0;
            }

            getHistory() {
                if (this.ws && this.isConnected) {
                    this.ws.send(JSON.stringify({
                        type: 'get_agent_history'
                    }));
                    this.addEventLog('system', '📚 请求获取Agent执行历史...');
                }
            }

            toggleAutoScroll() {
                this.autoScroll = !this.autoScroll;
                this.autoScrollBtn.textContent = `自动滚动: ${this.autoScroll ? '开' : '关'}`;
            }

            // 发送心跳
            sendHeartbeat() {
                if (this.ws && this.isConnected) {
                    this.ws.send(JSON.stringify({ type: 'ping' }));
                }
            }
        }

        // 初始化应用
        const viewer = new AgentStreamViewer();

        // 定期发送心跳
        setInterval(() => {
            viewer.sendHeartbeat();
        }, 30000);

        // 页面加载完成后的提示
        window.addEventListener('load', () => {
            viewer.addEventLog('system', '🚀 Agent流式传输测试页面已加载');
            viewer.addEventLog('system', '💡 请先创建一个会话，然后连接WebSocket开始测试');
        });
    </script>
</body>
</html>
