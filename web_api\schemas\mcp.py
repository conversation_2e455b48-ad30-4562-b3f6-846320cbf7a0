"""
MCP服务管理相关的数据模型
"""
from typing import Dict, List, Optional, Any
from enum import Enum
from pydantic import BaseModel, Field, validator


class MCPConnectionType(str, Enum):
    """MCP连接类型"""
    STDIO = "stdio"
    SSE = "sse"


class MCPServerStatus(str, Enum):
    """MCP服务器状态"""
    DISCONNECTED = "disconnected"
    CONNECTING = "connecting"
    CONNECTED = "connected"
    ERROR = "error"


class MCPServerConfigBase(BaseModel):
    """MCP服务器配置基础模型"""
    name: str = Field(..., description="服务器名称")
    type: MCPConnectionType = Field(..., description="连接类型")
    description: Optional[str] = Field(None, description="服务器描述")
    enabled: bool = Field(True, description="是否启用")


class MCPServerConfigCreate(MCPServerConfigBase):
    """创建MCP服务器配置的请求模型"""
    # stdio连接配置
    command: Optional[str] = Field(None, description="stdio连接的命令")
    args: List[str] = Field(default_factory=list, description="stdio连接的参数")
    
    # sse连接配置
    url: Optional[str] = Field(None, description="sse连接的URL")
    
    @validator('command')
    def validate_stdio_config(cls, v, values):
        """验证stdio配置"""
        if values.get('type') == MCPConnectionType.STDIO and not v:
            raise ValueError("stdio连接类型必须提供command")
        return v
    
    @validator('url')
    def validate_sse_config(cls, v, values):
        """验证sse配置"""
        if values.get('type') == MCPConnectionType.SSE and not v:
            raise ValueError("sse连接类型必须提供url")
        return v


class MCPServerConfigUpdate(BaseModel):
    """更新MCP服务器配置的请求模型"""
    name: Optional[str] = Field(None, description="服务器名称")
    description: Optional[str] = Field(None, description="服务器描述")
    enabled: Optional[bool] = Field(None, description="是否启用")
    
    # stdio连接配置
    command: Optional[str] = Field(None, description="stdio连接的命令")
    args: Optional[List[str]] = Field(None, description="stdio连接的参数")
    
    # sse连接配置
    url: Optional[str] = Field(None, description="sse连接的URL")


class MCPToolInfo(BaseModel):
    """MCP工具信息"""
    name: str = Field(..., description="工具名称")
    description: str = Field(..., description="工具描述")
    parameters: Dict[str, Any] = Field(default_factory=dict, description="工具参数")


class MCPServerInfo(MCPServerConfigBase):
    """MCP服务器信息响应模型"""
    server_id: str = Field(..., description="服务器ID")
    status: MCPServerStatus = Field(..., description="连接状态")
    
    # stdio连接配置
    command: Optional[str] = Field(None, description="stdio连接的命令")
    args: List[str] = Field(default_factory=list, description="stdio连接的参数")
    
    # sse连接配置
    url: Optional[str] = Field(None, description="sse连接的URL")
    
    # 运行时信息
    tools: List[MCPToolInfo] = Field(default_factory=list, description="可用工具列表")
    error_message: Optional[str] = Field(None, description="错误信息")
    connected_at: Optional[str] = Field(None, description="连接时间")
    
    class Config:
        use_enum_values = True


class MCPServerListResponse(BaseModel):
    """MCP服务器列表响应"""
    servers: List[MCPServerInfo] = Field(..., description="服务器列表")
    total: int = Field(..., description="总数")


class MCPConnectionRequest(BaseModel):
    """MCP连接请求"""
    force: bool = Field(False, description="是否强制重连")


class MCPConnectionResponse(BaseModel):
    """MCP连接响应"""
    success: bool = Field(..., description="是否成功")
    message: str = Field(..., description="响应消息")
    server_info: Optional[MCPServerInfo] = Field(None, description="服务器信息")


class MCPOperationResponse(BaseModel):
    """MCP操作响应"""
    success: bool = Field(..., description="是否成功")
    message: str = Field(..., description="响应消息")
    data: Optional[Dict[str, Any]] = Field(None, description="响应数据")


class MCPServerStatusResponse(BaseModel):
    """MCP服务器状态响应"""
    server_id: str = Field(..., description="服务器ID")
    status: MCPServerStatus = Field(..., description="连接状态")
    tools_count: int = Field(..., description="工具数量")
    error_message: Optional[str] = Field(None, description="错误信息")
    uptime: Optional[int] = Field(None, description="运行时间(秒)")
    
    class Config:
        use_enum_values = True
