# Web API Playwright MCP 自动启动集成

## 修改概述

本次修改解决了 `run_web_api.py` 启动 Web API 服务时没有自动连接 Playwright MCP 的问题。通过对比 `run_with_playwright_mcp.py` 的启动方式，我们在 Web API 的后台任务启动逻辑中添加了 Manus 实例的预初始化，确保 Playwright MCP 在服务启动时就自动连接。

## 问题分析

### 原始问题

- `run_web_api.py` 启动的 Web API 服务没有 Playwright MCP 功能
- `run_with_playwright_mcp.py` 启动时能正常集成 Playwright MCP
- 两种启动方式的差异导致功能不一致

### 根因分析

1. **`run_with_playwright_mcp.py` 的工作方式**：

   - 直接调用 `Manus.create()` 创建实例
   - `Manus.create()` 内部会自动调用 `initialize_mcp_servers()`
   - 读取 `config/mcp.json` 配置并连接所有 MCP 服务器

2. **`run_web_api.py` 的原始工作方式**：
   - 只启动 FastAPI 应用和后台任务
   - Manus 实例在处理具体任务时才临时创建
   - 没有在启动时预初始化 MCP 连接

## 修改内容

### 1. 修改 `web_api/services/background.py`

#### 添加全局 Manus 实例管理

```python
# 全局 Manus 实例（用于预初始化 MCP 连接）
_global_manus_instance = None

def get_global_manus_instance():
    """获取全局 Manus 实例"""
    return _global_manus_instance
```

#### 添加 MCP 初始化函数

```python
async def initialize_manus_with_mcp():
    """初始化 Manus 实例并连接 MCP 服务器"""
    global _global_manus_instance

    try:
        logger.info("🚀 初始化 Manus 实例并连接 MCP 服务器...")

        # 创建 Manus 实例（会自动连接配置文件中的 MCP 服务器）
        _global_manus_instance = await Manus.create()

        # 检查连接状态
        if _global_manus_instance.mcp_clients.sessions:
            available_tools = list(_global_manus_instance.mcp_clients.tool_map.keys())
            playwright_tools = [
                tool for tool in available_tools if "playwright" in tool.lower() or "browser" in tool.lower()
            ]

            logger.info(f"✅ 成功连接到 {len(_global_manus_instance.mcp_clients.sessions)} 个 MCP 服务器！")
            logger.info(f"📋 发现 {len(available_tools)} 个工具，其中 {len(playwright_tools)} 个 Playwright/浏览器工具")

            if playwright_tools:
                logger.info("🎭 Playwright MCP 集成成功，可用工具包括:")
                for tool in playwright_tools[:5]:  # 只显示前5个
                    logger.info(f"   - {tool}")
                if len(playwright_tools) > 5:
                    logger.info(f"   - ... 还有 {len(playwright_tools) - 5} 个工具")
        else:
            logger.warning("⚠️ 未检测到 MCP 服务器连接")

    except Exception as e:
        logger.error(f"❌ 初始化 Manus 和 MCP 连接时出错: {str(e)}")
        _global_manus_instance = None
```

#### 修改启动任务函数

```python
async def start_background_tasks():
    """启动所有后台任务"""
    logger.info("启动后台服务")

    # 初始化状态管理器（处理重新排队）
    await state_manager.initialize_after_startup()

    # 初始化 Manus 实例并连接 MCP 服务器
    await initialize_manus_with_mcp()

    # 创建后台任务
    tasks = [
        asyncio.create_task(task_executor()),
        asyncio.create_task(cleanup_task())
    ]

    logger.info("后台任务已启动")
    return tasks
```

### 2. 修改 `web_api/services/agent_adapter.py`

#### 修改任务执行逻辑

```python
# 使用全局 Agent 实例，如果不存在则创建新的
from .background import get_global_manus_instance
agent = get_global_manus_instance()
if agent is None:
    # 后备方案：如果全局实例不可用，创建新的
    logger.warning("全局 Manus 实例不可用，创建临时实例")
    agent = await Manus.create()
else:
    logger.info("使用预初始化的全局 Manus 实例（包含 MCP 连接）")
```

#### 修改工具列表获取逻辑

```python
async def get_available_tools(self) -> list:
    """获取可用工具列表（静态返回）"""
    # 从全局 Manus agent 中获取工具信息
    try:
        from .background import get_global_manus_instance
        agent = get_global_manus_instance()

        if agent is None:
            # 后备方案：如果全局实例不可用，创建临时实例
            logger.warning("全局 Manus 实例不可用，创建临时实例获取工具列表")
            agent = await Manus.create()
            should_cleanup = True
        else:
            should_cleanup = False

        # ... 获取工具逻辑 ...

        # 只有临时创建的实例才需要清理
        if should_cleanup:
            await agent.cleanup()

        return tools
```

### 3. 修改 `web_api/main.py`

#### 添加应用关闭时的资源清理

```python
@asynccontextmanager
async def lifespan(app: FastAPI):
    """应用生命周期管理"""
    # 启动时执行
    print("🚀 启动后台任务...")
    await start_background_tasks()
    print("✅ 后台任务启动完成")

    yield

    # 关闭时执行
    print("🧹 清理资源...")
    from .services.background import get_global_manus_instance
    global_manus = get_global_manus_instance()
    if global_manus:
        try:
            await global_manus.cleanup()
            print("✅ Manus 实例清理完成")
        except Exception as e:
            print(f"⚠️ Manus 清理时出错: {e}")
    print("✅ 清理完成")
```

## 修改效果

### 启动时的日志输出

现在通过 `python run_web_api.py` 启动时，会看到类似以下的日志：

```
🚀 启动RPA Agent简化版API服务
📡 监听地址: 0.0.0.0:8000
🔗 WebSocket: ws://0.0.0.0:8000/ws/sessions/{session_id}
📖 API文档: http://0.0.0.0:8000/docs
🚀 启动后台任务...
🚀 初始化 Manus 实例并连接 MCP 服务器...
✅ 成功连接到 2 个 MCP 服务器！
📋 发现 28 个工具，其中 25 个 Playwright/浏览器工具
🎭 Playwright MCP 集成成功，可用工具包括:
   - browser_navigate
   - browser_take_screenshot
   - browser_click
   - browser_type
   - browser_tab_new
   - ... 还有 20 个工具
✅ 后台任务启动完成
```

### 功能验证

1. **工具可用性**：Web API 现在可以访问所有 Playwright 工具
2. **性能优化**：重用全局 Manus 实例，避免重复创建和连接
3. **错误处理**：提供后备方案，确保服务稳定性
4. **资源管理**：应用关闭时正确清理资源

## 技术要点

### 1. 单例模式

- 使用全局变量管理 Manus 实例
- 避免重复创建和 MCP 连接开销
- 提供获取实例的统一接口

### 2. 错误容错

- 全局实例不可用时的后备方案
- 异常处理和日志记录
- 优雅的资源清理

### 3. 生命周期管理

- 应用启动时初始化
- 应用关闭时清理资源
- 异步操作的正确处理

## 配置要求

确保 `config/mcp.json` 中有正确的 Playwright MCP 配置：

```json
{
  "mcpServers": {
    "playwright": {
      "type": "stdio",
      "command": "node",
      "args": [
        "external/playwright-mcp/cli.js",
        "--browser=chrome",
        "--no-sandbox",
        "--config=playwright-mcp-config.json"
      ]
    }
  }
}
```

## 总结

通过本次修改，`run_web_api.py` 现在与 `run_with_playwright_mcp.py` 具有相同的 Playwright MCP 集成能力，确保了两种启动方式的功能一致性。修改遵循了以下原则：

1. **不破坏原有逻辑**：保持现有功能不变
2. **性能优化**：重用连接，避免重复创建
3. **错误处理**：提供容错机制
4. **资源管理**：正确的生命周期管理

现在用户可以通过 `python run_web_api.py` 启动 Web API 服务，并自动获得完整的 Playwright MCP 功能支持。
