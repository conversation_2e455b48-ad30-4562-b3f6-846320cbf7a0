"""
MCP服务管理业务逻辑
"""

import asyncio
import json
from datetime import datetime
from pathlib import Path
from typing import Any, Dict, List, Optional

from app.agent.manus import Manus
from app.config import PROJECT_ROOT, MCPServerConfig, config
from app.logger import logger
from app.tool.mcp import MCPClients
from web_api.schemas.mcp import (
    MCPConnectionType,
    MCPServerConfigCreate,
    MCPServerConfigUpdate,
    MCPServerInfo,
    MCPServerStatus,
    MCPToolInfo,
)


class MCPManagerService:
    """MCP服务管理器"""

    def __init__(self):
        self._manus_instance: Optional[Manus] = None
        self._server_status: Dict[str, Dict[str, Any]] = {}
        self._config_file_path = PROJECT_ROOT / "config" / "mcp.json"

    async def get_manus_instance(self) -> Manus:
        """获取Manus实例，如果不存在则创建"""
        if self._manus_instance is None:
            self._manus_instance = await Manus.create()
        return self._manus_instance

    def _load_config_file(self) -> Dict[str, Any]:
        """加载MCP配置文件"""
        try:
            if self._config_file_path.exists():
                with self._config_file_path.open("r", encoding="utf-8") as f:
                    return json.load(f)
            return {"mcpServers": {}}
        except Exception as e:
            logger.error(f"Failed to load MCP config: {e}")
            return {"mcpServers": {}}

    def _save_config_file(self, config_data: Dict[str, Any]) -> None:
        """保存MCP配置文件"""
        try:
            # 确保目录存在
            self._config_file_path.parent.mkdir(parents=True, exist_ok=True)

            with self._config_file_path.open("w", encoding="utf-8") as f:
                json.dump(config_data, f, indent=2, ensure_ascii=False)

            logger.info(f"MCP config saved to {self._config_file_path}")
        except Exception as e:
            logger.error(f"Failed to save MCP config: {e}")
            raise

    def _get_server_status(self, server_id: str) -> MCPServerStatus:
        """获取服务器连接状态"""
        manus = self._manus_instance
        if not manus:
            return MCPServerStatus.DISCONNECTED

        if server_id in manus.mcp_clients.sessions:
            return MCPServerStatus.CONNECTED

        # 检查是否在连接中
        status_info = self._server_status.get(server_id, {})
        if status_info.get("connecting", False):
            return MCPServerStatus.CONNECTING

        if status_info.get("error"):
            return MCPServerStatus.ERROR

        return MCPServerStatus.DISCONNECTED

    def _get_server_tools(self, server_id: str) -> List[MCPToolInfo]:
        """获取服务器提供的工具列表"""
        manus = self._manus_instance
        if not manus or server_id not in manus.mcp_clients.sessions:
            return []

        tools = []
        for tool_name, tool in manus.mcp_clients.tool_map.items():
            if hasattr(tool, "server_id") and tool.server_id == server_id:
                tools.append(
                    MCPToolInfo(
                        name=tool.name,
                        description=tool.description,
                        parameters=tool.parameters,
                    )
                )

        return tools

    async def list_servers(self) -> List[MCPServerInfo]:
        """获取所有MCP服务器列表"""
        config_data = self._load_config_file()
        servers = []

        for server_id, server_config in config_data.get("mcpServers", {}).items():
            status = self._get_server_status(server_id)
            tools = self._get_server_tools(server_id)

            # 获取状态信息
            status_info = self._server_status.get(server_id, {})

            server_info = MCPServerInfo(
                server_id=server_id,
                name=server_config.get("name", server_id),
                type=MCPConnectionType(server_config["type"]),
                description=server_config.get("description", ""),
                enabled=server_config.get("enabled", True),
                status=status,
                command=server_config.get("command"),
                args=server_config.get("args", []),
                url=server_config.get("url"),
                tools=tools,
                error_message=status_info.get("error"),
                connected_at=status_info.get("connected_at"),
            )
            servers.append(server_info)

        return servers

    async def get_server(self, server_id: str) -> Optional[MCPServerInfo]:
        """获取指定MCP服务器信息"""
        servers = await self.list_servers()
        for server in servers:
            if server.server_id == server_id:
                return server
        return None

    async def create_server(
        self, server_config: MCPServerConfigCreate
    ) -> MCPServerInfo:
        """创建新的MCP服务器配置"""
        config_data = self._load_config_file()

        # 生成server_id（使用name，确保唯一性）
        server_id = server_config.name.lower().replace(" ", "_")
        counter = 1
        original_id = server_id
        while server_id in config_data.get("mcpServers", {}):
            server_id = f"{original_id}_{counter}"
            counter += 1

        # 构建配置
        new_config = {
            "name": server_config.name,
            "type": server_config.type.value,
            "description": server_config.description or "",
            "enabled": server_config.enabled,
        }

        if server_config.type == MCPConnectionType.STDIO:
            new_config["command"] = server_config.command
            new_config["args"] = server_config.args
        else:  # SSE
            new_config["url"] = server_config.url

        # 保存配置
        config_data["mcpServers"][server_id] = new_config
        self._save_config_file(config_data)

        # 重新加载配置到全局config
        await self._reload_global_config()

        logger.info(f"Created MCP server config: {server_id}")

        # 返回创建的服务器信息
        return MCPServerInfo(
            server_id=server_id,
            name=server_config.name,
            type=server_config.type,
            description=server_config.description or "",
            enabled=server_config.enabled,
            status=MCPServerStatus.DISCONNECTED,
            command=server_config.command,
            args=server_config.args,
            url=server_config.url,
            tools=[],
            error_message=None,
            connected_at=None,
        )

    async def update_server(
        self, server_id: str, server_update: MCPServerConfigUpdate
    ) -> Optional[MCPServerInfo]:
        """更新MCP服务器配置"""
        config_data = self._load_config_file()

        if server_id not in config_data.get("mcpServers", {}):
            return None

        server_config = config_data["mcpServers"][server_id]

        # 更新配置
        if server_update.name is not None:
            server_config["name"] = server_update.name
        if server_update.description is not None:
            server_config["description"] = server_update.description
        if server_update.enabled is not None:
            server_config["enabled"] = server_update.enabled
        if server_update.command is not None:
            server_config["command"] = server_update.command
        if server_update.args is not None:
            server_config["args"] = server_update.args
        if server_update.url is not None:
            server_config["url"] = server_update.url

        # 保存配置
        self._save_config_file(config_data)

        # 重新加载配置到全局config
        await self._reload_global_config()

        logger.info(f"Updated MCP server config: {server_id}")

        # 返回更新后的服务器信息
        return await self.get_server(server_id)

    async def delete_server(self, server_id: str) -> bool:
        """删除MCP服务器配置"""
        # 先断开连接
        await self.disconnect_server(server_id)

        config_data = self._load_config_file()

        if server_id not in config_data.get("mcpServers", {}):
            return False

        # 删除配置
        del config_data["mcpServers"][server_id]
        self._save_config_file(config_data)

        # 清理状态信息
        self._server_status.pop(server_id, None)

        # 重新加载配置到全局config
        await self._reload_global_config()

        logger.info(f"Deleted MCP server config: {server_id}")
        return True

    async def connect_server(self, server_id: str, force: bool = False) -> bool:
        """连接到MCP服务器"""
        try:
            # 获取配置
            config_data = self._load_config_file()
            server_config = config_data.get("mcpServers", {}).get(server_id)

            if not server_config:
                raise ValueError(f"Server {server_id} not found")

            if not server_config.get("enabled", True):
                raise ValueError(f"Server {server_id} is disabled")

            # 获取Manus实例
            manus = await self.get_manus_instance()

            # 检查是否已连接
            if not force and server_id in manus.mcp_clients.sessions:
                logger.info(f"Server {server_id} already connected")
                return True

            # 设置连接状态
            self._server_status[server_id] = {"connecting": True, "error": None}

            # 根据类型连接
            if server_config["type"] == "stdio":
                await manus.connect_mcp_server(
                    server_config["command"],
                    server_id,
                    use_stdio=True,
                    stdio_args=server_config.get("args", []),
                )
            elif server_config["type"] == "sse":
                await manus.connect_mcp_server(
                    server_config["url"], server_id, use_stdio=False
                )
            else:
                raise ValueError(
                    f"Unsupported connection type: {server_config['type']}"
                )

            # 更新状态
            self._server_status[server_id] = {
                "connecting": False,
                "error": None,
                "connected_at": datetime.now().isoformat(),
            }

            logger.info(f"Successfully connected to MCP server: {server_id}")
            return True

        except Exception as e:
            error_msg = str(e)
            logger.error(f"Failed to connect to MCP server {server_id}: {error_msg}")

            # 更新错误状态
            self._server_status[server_id] = {"connecting": False, "error": error_msg}

            return False

    async def disconnect_server(self, server_id: str) -> bool:
        """断开MCP服务器连接"""
        try:
            manus = await self.get_manus_instance()

            if server_id not in manus.mcp_clients.sessions:
                logger.info(f"Server {server_id} not connected")
                return True

            # 断开连接
            await manus.disconnect_mcp_server(server_id)

            # 清理状态
            self._server_status[server_id] = {"connecting": False, "error": None}

            logger.info(f"Successfully disconnected from MCP server: {server_id}")
            return True

        except Exception as e:
            error_msg = str(e)
            logger.error(
                f"Failed to disconnect from MCP server {server_id}: {error_msg}"
            )

            # 更新错误状态
            self._server_status[server_id] = {"connecting": False, "error": error_msg}

            return False

    async def get_server_status(self, server_id: str) -> Dict[str, Any]:
        """获取服务器详细状态"""
        manus = await self.get_manus_instance()
        status_info = self._server_status.get(server_id, {})

        # 基础状态信息
        result = {
            "server_id": server_id,
            "status": self._get_server_status(server_id).value,
            "tools_count": len(self._get_server_tools(server_id)),
            "error_message": status_info.get("error"),
            "uptime": None,
        }

        # 计算运行时间
        if status_info.get("connected_at"):
            try:
                connected_time = datetime.fromisoformat(status_info["connected_at"])
                uptime = (datetime.now() - connected_time).total_seconds()
                result["uptime"] = int(uptime)
            except Exception:
                pass

        return result

    async def _reload_global_config(self):
        """重新加载全局配置"""
        try:
            # 使用config对象的reload方法
            config.reload_mcp_config()
            logger.info("Global MCP config reloaded")
        except Exception as e:
            logger.error(f"Failed to reload global config: {e}")


# 全局MCP管理器实例
mcp_manager = MCPManagerService()
