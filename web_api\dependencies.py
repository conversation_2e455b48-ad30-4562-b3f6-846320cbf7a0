"""
依赖注入模块
"""
from typing import Optional, Generator
from fastapi import Header, HTTPException, Depends # type: ignore
from sqlalchemy.orm import Session
from web_api.database import get_db


async def get_auth_token(authorization: Optional[str] = Header(None)) -> Optional[str]:
    """
    从请求头中获取认证令牌

    Args:
        authorization: 认证头

    Returns:
        认证令牌
    """
    if authorization is None:
        return None

    if not authorization.startswith("Bearer "):
        raise HTTPException(status_code=400, detail="认证头格式不正确")

    token = authorization.split(" ")[1]
    # 这里可以添加 token 验证逻辑
    if token == "invalid":
        raise HTTPException(status_code=400, detail="Token 不正确")
    return None

# 数据库会话依赖
def get_database_session() -> Generator[Session, None, None]:
    """获取数据库会话"""
    db = next(get_db())
    try:
        yield db
    finally:
        db.close()
