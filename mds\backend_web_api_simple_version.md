# RPA 代理后端 Web API 简易版设计方案

## 📋 简化目标

基于原有设计方案，创建一个无需登录、临时状态、不依赖外部数据库的简易版本。专注于核心功能的快速实现和验证。

## 🎯 简化原则

1. **无认证**: 跳过所有认证和授权机制
2. **内存状态**: 使用内存存储替代 Redis/MySQL
3. **临时会话**: 会话重启后丢失，适合演示和开发
4. **核心功能**: 专注于任务执行和实时通信
5. **快速启动**: 最小化依赖，开箱即用

## 🔧 简化架构

### 核心组件（简化版）

```
FastAPI应用 ──→ 内存状态管理 ──→ Agent执行器
    │                │              │
    ├── WebSocket     ├── 会话字典    ├── 现有工具
    ├── RESTful       ├── 任务队列    └── 实时事件
    └── 静态文件       └── 事件广播
```

### 技术栈（最小化）

- **Web 框架**: FastAPI (现有)
- **实时通信**: 内置 WebSocket
- **状态存储**: Python 字典 + asyncio
- **任务队列**: asyncio.Queue
- **日志系统**: 现有 logger 扩展

## 📡 简化 API 设计

### 1. RESTful API（核心接口）

#### 1.1 会话管理（简化）

```http
POST /api/sessions
# 创建临时会话
Response: {
  "session_id": "uuid",
  "status": "active",
  "created_at": "2025-01-16T10:00:00Z"
}

GET /api/sessions/{session_id}
# 获取会话状态
Response: {
  "session_id": "uuid",
  "status": "active|expired",
  "task_count": 2,
  "created_at": "timestamp"
}

# 注意：删除SESSION接口简化为自动过期
```

#### 1.2 任务管理（简化）

```http
POST /api/sessions/{session_id}/tasks
# 创建任务
Request: {
  "message": "用户输入的任务描述"
}
Response: {
  "task_id": "uuid",
  "status": "queued"
}

GET /api/sessions/{session_id}/tasks/{task_id}
# 获取任务状态
Response: {
  "task_id": "uuid",
  "status": "queued|running|completed|error",
  "current_step": 1,
  "max_steps": 20,
  "result": "结果或null"
}

POST /api/sessions/{session_id}/tasks/{task_id}/cancel
# 取消任务（保留）
```

#### 1.3 工具信息（简化）

```http
GET /api/tools
# 获取工具列表（静态返回）
Response: {
  "tools": [
    {
      "name": "python_execute",
      "description": "执行Python代码",
      "category": "core"
    }
  ]
}
```

#### 1.4 健康检查

```http
GET /api/health
# 服务健康状态
Response: {
  "status": "ok",
  "sessions": 3,
  "active_tasks": 1,
  "uptime": 3600
}
```

### 2. WebSocket API（保持不变）

```typescript
// 连接端点
WS /ws/sessions/{session_id}

// 消息格式保持原设计
{
  "type": "task_status|step_update|tool_call|log",
  "timestamp": "2025-01-16T10:00:00Z",
  "data": {...}
}
```

## 🏗️ 简化实现结构

### 1. 内存状态管理器

```python
from typing import Dict, Optional
import asyncio
from datetime import datetime, timedelta
import uuid

class SimpleStateManager:
    """简化的内存状态管理器"""

    def __init__(self):
        self.sessions: Dict[str, dict] = {}
        self.tasks: Dict[str, dict] = {}
        self.websockets: Dict[str, list] = {}  # session_id -> [websocket]
        self.task_queues: Dict[str, asyncio.Queue] = {}

    # 会话管理
    async def create_session(self) -> str:
        session_id = str(uuid.uuid4())
        self.sessions[session_id] = {
            "session_id": session_id,
            "status": "active",
            "created_at": datetime.now(),
            "task_ids": []
        }
        self.websockets[session_id] = []
        self.task_queues[session_id] = asyncio.Queue()
        return session_id

    def get_session(self, session_id: str) -> Optional[dict]:
        return self.sessions.get(session_id)

    # 任务管理
    async def create_task(self, session_id: str, message: str) -> str:
        task_id = str(uuid.uuid4())
        task = {
            "task_id": task_id,
            "session_id": session_id,
            "message": message,
            "status": "queued",
            "current_step": 0,
            "max_steps": 20,
            "created_at": datetime.now(),
            "result": None
        }
        self.tasks[task_id] = task
        if session_id in self.sessions:
            self.sessions[session_id]["task_ids"].append(task_id)

        # 添加到执行队列
        await self.task_queues[session_id].put(task_id)
        return task_id

    def get_task(self, task_id: str) -> Optional[dict]:
        return self.tasks.get(task_id)

    def update_task(self, task_id: str, **updates):
        if task_id in self.tasks:
            self.tasks[task_id].update(updates)

    # WebSocket管理
    def add_websocket(self, session_id: str, websocket):
        if session_id in self.websockets:
            self.websockets[session_id].append(websocket)

    def remove_websocket(self, session_id: str, websocket):
        if session_id in self.websockets:
            try:
                self.websockets[session_id].remove(websocket)
            except ValueError:
                pass

    async def broadcast_to_session(self, session_id: str, message: dict):
        """向会话的所有WebSocket连接广播消息"""
        if session_id in self.websockets:
            dead_connections = []
            for ws in self.websockets[session_id]:
                try:
                    await ws.send_json(message)
                except:
                    dead_connections.append(ws)

            # 清理断开的连接
            for ws in dead_connections:
                self.remove_websocket(session_id, ws)

    # 清理过期数据
    async def cleanup_expired(self, max_age_hours: int = 24):
        """清理过期的会话和任务"""
        cutoff = datetime.now() - timedelta(hours=max_age_hours)
        expired_sessions = []

        for session_id, session in self.sessions.items():
            if session["created_at"] < cutoff:
                expired_sessions.append(session_id)

        for session_id in expired_sessions:
            # 清理会话相关的所有数据
            if session_id in self.sessions:
                task_ids = self.sessions[session_id]["task_ids"]
                for task_id in task_ids:
                    self.tasks.pop(task_id, None)

                self.sessions.pop(session_id)
                self.websockets.pop(session_id, None)
                self.task_queues.pop(session_id, None)
```

### 2. 简化的 Agent 适配器

```python
import asyncio
from app.agent.manus import ManusAgent

class SimpleAgentAdapter:
    """简化的Agent适配器，添加事件推送"""

    def __init__(self, state_manager: SimpleStateManager):
        self.state_manager = state_manager
        self.running_tasks: Dict[str, asyncio.Task] = {}

    async def execute_task(self, task_id: str):
        """执行任务并推送事件"""
        task = self.state_manager.get_task(task_id)
        if not task:
            return

        session_id = task["session_id"]

        try:
            # 更新任务状态为运行中
            self.state_manager.update_task(task_id,
                status="running",
                start_time=datetime.now()
            )

            # 推送任务开始事件
            await self.state_manager.broadcast_to_session(session_id, {
                "type": "task_status",
                "timestamp": datetime.now().isoformat(),
                "data": {
                    "task_id": task_id,
                    "status": "running",
                    "current_step": 0
                }
            })

            # 创建Agent实例并执行
            agent = ManusAgent()

            # 包装Agent的执行过程以捕获事件
            result = await self._execute_with_events(agent, task, session_id)

            # 更新任务完成状态
            self.state_manager.update_task(task_id,
                status="completed",
                end_time=datetime.now(),
                result=result
            )

            # 推送完成事件
            await self.state_manager.broadcast_to_session(session_id, {
                "type": "task_status",
                "timestamp": datetime.now().isoformat(),
                "data": {
                    "task_id": task_id,
                    "status": "completed",
                    "result": result
                }
            })

        except Exception as e:
            # 处理错误
            self.state_manager.update_task(task_id,
                status="error",
                end_time=datetime.now(),
                error_message=str(e)
            )

            await self.state_manager.broadcast_to_session(session_id, {
                "type": "task_status",
                "timestamp": datetime.now().isoformat(),
                "data": {
                    "task_id": task_id,
                    "status": "error",
                    "error": str(e)
                }
            })

    async def _execute_with_events(self, agent, task, session_id):
        """包装Agent执行过程，添加事件推送"""
        # 这里需要根据实际的Agent接口来适配
        # 目前简化为直接调用

        # 推送思考事件
        await self.state_manager.broadcast_to_session(session_id, {
            "type": "step_update",
            "timestamp": datetime.now().isoformat(),
            "data": {
                "task_id": task["task_id"],
                "step": 1,
                "action": "思考中...",
                "thoughts": f"正在处理任务: {task['message']}"
            }
        })

        # 实际执行（需要根据现有Agent接口调整）
        result = await agent.run(task["message"])

        return result

# 全局状态管理器实例
state_manager = SimpleStateManager()
agent_adapter = SimpleAgentAdapter(state_manager)
```

### 3. 任务执行器

```python
async def task_executor():
    """后台任务执行器"""
    while True:
        try:
            # 检查所有会话的任务队列
            for session_id, queue in state_manager.task_queues.items():
                try:
                    # 非阻塞检查队列
                    task_id = queue.get_nowait()

                    # 启动任务执行
                    task = asyncio.create_task(
                        agent_adapter.execute_task(task_id)
                    )
                    agent_adapter.running_tasks[task_id] = task

                except asyncio.QueueEmpty:
                    continue

        except Exception as e:
            print(f"Task executor error: {e}")

        await asyncio.sleep(0.1)  # 避免CPU占用过高

# 启动后台任务执行器
async def start_background_tasks():
    asyncio.create_task(task_executor())
    asyncio.create_task(cleanup_task())

async def cleanup_task():
    """定期清理过期数据"""
    while True:
        await asyncio.sleep(3600)  # 每小时清理一次
        await state_manager.cleanup_expired()
```

## 📁 文件结构（简化）

```
web_api/
├── __init__.py
├── main.py                 # FastAPI应用入口
├── routers/
│   ├── __init__.py
│   ├── sessions.py         # 会话路由
│   ├── tasks.py           # 任务路由
│   ├── tools.py           # 工具路由
│   └── websocket.py       # WebSocket路由
├── services/
│   ├── __init__.py
│   ├── state_manager.py   # 状态管理器
│   ├── agent_adapter.py   # Agent适配器
│   └── background.py      # 后台任务
├── schemas/
│   ├── __init__.py
│   ├── session.py         # 会话模式
│   └── task.py           # 任务模式
└── config.py              # 配置（简化）
```

## 🚀 快速启动配置

### 1. 简化配置文件

```python
# web_api/config.py
from pydantic_settings import BaseSettings

class SimpleConfig(BaseSettings):
    # Web API配置
    host: str = "0.0.0.0"
    port: int = 8000
    debug: bool = True

    # 资源限制（简化）
    max_sessions: int = 50
    max_tasks_per_session: int = 5
    task_timeout: int = 300
    session_max_age_hours: int = 24

    class Config:
        env_prefix = "WEB_API_"

config = SimpleConfig()
```

### 2. FastAPI 应用（简化）

```python
# web_api/main.py
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from contextlib import asynccontextmanager

from .routers import sessions, tasks, tools, websocket
from .services.background import start_background_tasks

@asynccontextmanager
async def lifespan(app: FastAPI):
    # 启动时执行
    await start_background_tasks()
    yield
    # 关闭时执行（可以添加清理逻辑）

def create_app() -> FastAPI:
    app = FastAPI(
        title="RPA Agent API (Simple)",
        description="RPA代理简化版API",
        version="1.0.0",
        lifespan=lifespan
    )

    # CORS配置（开发用）
    app.add_middleware(
        CORSMiddleware,
        allow_origins=["*"],
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )

    # 路由注册
    app.include_router(sessions.router, prefix="/api")
    app.include_router(tasks.router, prefix="/api")
    app.include_router(tools.router, prefix="/api")
    app.include_router(websocket.router, prefix="/ws")

    @app.get("/api/health")
    async def health_check():
        from .services.state_manager import state_manager
        return {
            "status": "ok",
            "sessions": len(state_manager.sessions),
            "tasks": len(state_manager.tasks),
            "active_connections": sum(
                len(conns) for conns in state_manager.websockets.values()
            )
        }

    return app

app = create_app()
```

### 3. 启动脚本（更新）

```python
# run_web_api.py (更新版)
#!/usr/bin/env python
"""
启动简化版Web API服务
"""
import uvicorn
from web_api.main import app
from web_api.config import config

if __name__ == "__main__":
    print(f"🚀 启动RPA Agent简化版API服务")
    print(f"📡 监听地址: {config.host}:{config.port}")
    print(f"🔗 WebSocket: ws://{config.host}:{config.port}/ws/sessions/{{session_id}}")
    print(f"📖 API文档: http://{config.host}:{config.port}/docs")

    uvicorn.run(
        app,
        host=config.host,
        port=config.port,
        reload=config.debug,
        log_level="info"
    )
```

## 🎯 开发优先级（简化版）

### Phase 1: 核心 API 实现 (3-5 天)

- [ ] 简化状态管理器
- [ ] 基础 REST API（会话、任务）
- [ ] Agent 适配器基础版本

### Phase 2: WebSocket 通信 (2-3 天)

- [ ] WebSocket 连接管理
- [ ] 实时事件推送
- [ ] 错误处理

### Phase 3: Agent 集成 (2-3 天)

- [ ] 现有 Agent 包装
- [ ] 工具调用事件捕获
- [ ] 执行状态同步

### Phase 4: 测试和优化 (1-2 天)

- [ ] 接口测试
- [ ] 内存管理优化
- [ ] 前端对接验证

## 🔄 与原方案的主要差异

| 方面       | 原方案               | 简化版           |
| ---------- | -------------------- | ---------------- |
| 认证       | API Key + 会话 Token | 无认证           |
| 存储       | Redis + MySQL        | 内存字典         |
| 会话       | 持久化               | 临时（重启丢失） |
| 扩展性     | 支持集群             | 单实例           |
| 数据恢复   | 支持                 | 不支持           |
| 部署复杂度 | 中等                 | 极简             |

## 📋 验证和测试

### 简单测试脚本

```python
# test_simple_api.py
import asyncio
import aiohttp
import json

async def test_api():
    base_url = "http://localhost:8000"

    async with aiohttp.ClientSession() as session:
        # 1. 创建会话
        async with session.post(f"{base_url}/api/sessions") as resp:
            session_data = await resp.json()
            session_id = session_data["session_id"]
            print(f"✅ 创建会话: {session_id}")

        # 2. 创建任务
        task_payload = {"message": "请帮我创建一个Python Hello World脚本"}
        async with session.post(
            f"{base_url}/api/sessions/{session_id}/tasks",
            json=task_payload
        ) as resp:
            task_data = await resp.json()
            task_id = task_data["task_id"]
            print(f"✅ 创建任务: {task_id}")

        # 3. 检查任务状态
        await asyncio.sleep(2)
        async with session.get(
            f"{base_url}/api/sessions/{session_id}/tasks/{task_id}"
        ) as resp:
            status_data = await resp.json()
            print(f"✅ 任务状态: {status_data['status']}")

if __name__ == "__main__":
    asyncio.run(test_api())
```

这个简化版本专注于核心功能，去除了复杂的依赖，适合快速开发和验证概念。可以作为 MVP 版本先行实现！
