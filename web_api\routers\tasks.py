"""
任务管理路由

提供任务的创建、查询、取消等 REST API
"""

from fastapi import APIRouter, HTTPException, status
from typing import List

from ..schemas.task import TaskCreate, TaskResponse, TaskList, TaskCancel
from ..services.state_manager import state_manager
from ..services.background import agent_adapter

router = APIRouter(prefix="/sessions/{session_id}/tasks", tags=["tasks"])


@router.post("", response_model=TaskResponse, status_code=status.HTTP_201_CREATED)
async def create_task(session_id: str, task_data: TaskCreate):
    """在指定会话中创建新任务"""
    # 检查会话是否存在
    session = state_manager.get_session(session_id)
    if not session:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"会话 {session_id} 不存在"
        )

    try:
        task_id = await state_manager.create_task(session_id, task_data.message)
        task = state_manager.get_task(task_id)

        if not task:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="任务创建失败"
            )

        return TaskResponse(
            task_id=task["task_id"],
            session_id=task["session_id"],
            message=task["message"],
            status=task["status"],
            current_step=task["current_step"],
            max_steps=task["max_steps"],
            created_at=task["created_at"],
            start_time=task.get("start_time"),
            end_time=task.get("end_time"),
            result=task.get("result"),
            error_message=task.get("error_message")
        )
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"创建任务失败: {str(e)}"
        )


@router.get("/{task_id}", response_model=TaskResponse)
async def get_task(session_id: str, task_id: str):
    """获取指定任务的信息"""
    # 检查会话是否存在
    session = state_manager.get_session(session_id)
    if not session:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"会话 {session_id} 不存在"
        )

    task = state_manager.get_task(task_id)
    if not task:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"任务 {task_id} 不存在"
        )

    # 验证任务属于指定会话
    if task["session_id"] != session_id:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"任务 {task_id} 不属于会话 {session_id}"
        )

    return TaskResponse(
        task_id=task["task_id"],
        session_id=task["session_id"],
        message=task["message"],
        status=task["status"],
        current_step=task["current_step"],
        max_steps=task["max_steps"],
        created_at=task["created_at"],
        start_time=task.get("start_time"),
        end_time=task.get("end_time"),
        result=task.get("result"),
        error_message=task.get("error_message")
    )


@router.get("", response_model=TaskList)
async def list_tasks(session_id: str):
    """获取指定会话的所有任务"""
    session = state_manager.get_session(session_id)
    if not session:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"会话 {session_id} 不存在"
        )

    tasks = state_manager.get_session_tasks(session_id)

    task_responses = [
        TaskResponse(
            task_id=task["task_id"],
            session_id=task["session_id"],
            message=task["message"],
            status=task["status"],
            current_step=task["current_step"],
            max_steps=task["max_steps"],
            created_at=task["created_at"],
            start_time=task.get("start_time"),
            end_time=task.get("end_time"),
            result=task.get("result"),
            error_message=task.get("error_message")
        )
        for task in tasks
    ]

    return TaskList(
        tasks=task_responses,
        total=len(task_responses)
    )


@router.post("/{task_id}/cancel", response_model=TaskCancel)
async def cancel_task(session_id: str, task_id: str):
    """取消指定任务"""
    # 检查会话是否存在
    session = state_manager.get_session(session_id)
    if not session:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"会话 {session_id} 不存在"
        )

    task = state_manager.get_task(task_id)
    if not task:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"任务 {task_id} 不存在"
        )

    # 验证任务属于指定会话
    if task["session_id"] != session_id:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"任务 {task_id} 不属于会话 {session_id}"
        )

    try:
        # 尝试取消任务
        cancelled = await agent_adapter.cancel_task(task_id)

        if not cancelled:
            # 如果 agent 中没有取消成功，尝试在状态管理器中取消
            cancelled = state_manager.cancel_task(task_id)

        message = "任务已成功取消" if cancelled else "任务无法取消（可能已完成或不在运行中）"

        return TaskCancel(
            task_id=task_id,
            cancelled=cancelled,
            message=message
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"取消任务失败: {str(e)}"
        )
