# 持久化存储优化记录

## 修改日期
2025-07-17

## 修改目的
根据用户要求，将会话和任务的存储从 `data` 目录迁移到 `datas` 目录，优化JSON文件持久化存储机制。

## 修改内容

### 1. 数据目录变更
- **原目录**: `PROJECT_ROOT / "data"`
- **新目录**: `PROJECT_ROOT / "datas"`

### 2. 修改的文件

#### web_api/services/state_manager.py
**修改位置**: 第31-34行
```python
# 修改前
self.data_dir = PROJECT_ROOT / "data"

# 修改后  
self.data_dir = PROJECT_ROOT / "datas"
```

### 3. 数据迁移
- 创建了新的 `datas` 目录
- 将现有的 `data/sessions.json` 和 `data/tasks.json` 复制到 `datas/` 目录
- 保持了所有现有数据的完整性

### 4. 持久化机制说明

#### 当前实现的功能
1. **自动加载**: 启动时从JSON文件自动加载会话和任务数据
2. **实时保存**: 在关键操作后自动保存数据到JSON文件
3. **数据完整性**: 支持datetime对象的序列化和反序列化
4. **错误处理**: 包含完善的异常处理机制

#### 触发保存的操作
- 创建新会话 (`create_session`)
- 创建新任务 (`create_task`) 
- 更新任务状态 (`update_task` - 当更新status、result或error_message时)
- 重新排队任务 (`_requeue_pending_tasks`)
- 清理过期数据 (`cleanup_expired`)

#### 数据格式
**sessions.json**:
```json
{
  "session_id": {
    "session_id": "uuid",
    "status": "active",
    "created_at": "2025-07-17T14:15:02.144713",
    "task_ids": ["task_id1", "task_id2"]
  }
}
```

**tasks.json**:
```json
{
  "task_id": {
    "task_id": "uuid",
    "session_id": "uuid", 
    "message": "任务描述",
    "status": "queued|running|completed|cancelled",
    "current_step": 0,
    "max_steps": 20,
    "created_at": "2025-07-17T14:15:02.144713",
    "start_time": "2025-07-17T14:15:02.144713",
    "end_time": "2025-07-17T14:15:02.144713",
    "result": "任务结果",
    "error_message": "错误信息"
  }
}
```

### 5. 测试验证

#### 测试内容
1. ✅ 数据目录路径正确性验证
2. ✅ 现有数据加载功能验证  
3. ✅ 新会话创建和持久化验证
4. ✅ 新任务创建和持久化验证
5. ✅ 任务状态更新和持久化验证

#### 测试结果
- 所有测试通过
- 数据目录成功从 `data` 迁移到 `datas`
- 持久化功能正常工作
- 新创建的会话和任务能正确保存到JSON文件
- 任务状态更新能实时持久化

### 6. 影响范围
- **核心影响**: `web_api/services/state_manager.py`
- **数据迁移**: `data/` → `datas/`
- **向后兼容**: 保持了所有现有数据和API接口不变

### 7. 注意事项
1. 确保 `datas` 目录有适当的读写权限
2. JSON文件使用UTF-8编码，支持中文内容
3. datetime对象通过ISO格式字符串进行序列化
4. 系统启动时会自动重新排队未完成的任务

### 8. 后续建议
1. 可以考虑添加数据备份机制
2. 可以实现定期压缩历史数据
3. 可以添加数据完整性校验
4. 可以考虑实现增量保存以提高性能

## 修改状态
✅ 已完成并测试通过
