"""
工具信息路由

提供获取可用工具列表的 REST API
"""

from fastapi import APIRouter, HTTPException, status
from typing import List, Dict, Any

from ..services.background import agent_adapter

router = APIRouter(prefix="/tools", tags=["tools"])


@router.get("", response_model=Dict[str, Any])
async def get_tools():
    """获取所有可用工具列表"""
    try:
        tools = await agent_adapter.get_available_tools()

        return {
            "tools": tools,
            "total": len(tools),
            "categories": {
                "core": [tool for tool in tools if tool.get("category") == "core"],
                "mcp": [tool for tool in tools if tool.get("category") == "mcp"]
            }
        }
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取工具列表失败: {str(e)}"
        )


@router.get("/categories", response_model=Dict[str, List[str]])
async def get_tool_categories():
    """获取工具分类信息"""
    try:
        tools = await agent_adapter.get_available_tools()

        categories = {}
        for tool in tools:
            category = tool.get("category", "unknown")
            if category not in categories:
                categories[category] = []
            categories[category].append(tool["name"])

        return categories
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取工具分类失败: {str(e)}"
        )
