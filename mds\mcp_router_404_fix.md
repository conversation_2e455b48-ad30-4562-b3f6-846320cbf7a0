# MCP 路由 404 错误修复记录

## 问题描述

用户通过以下方式启动 Web API 服务：
```bash
.venv\Scripts\activate  
python run_web_api.py
```

发现所有 MCP 相关接口都返回 404 错误，无法访问。

## 问题分析

通过检查代码发现，虽然 `web_api/routers/mcp_router.py` 文件存在并且定义了完整的 MCP 管理接口，但是这些路由没有被正确注册到 FastAPI 应用中。

### 具体问题：

1. **路由模块未导入**: 在 `web_api/routers/__init__.py` 中没有导入 `mcp_router` 模块
2. **路由未注册**: 在 `web_api/main.py` 中没有将 MCP 路由注册到 FastAPI 应用

## 修复方案

### 1. 修改 `web_api/routers/__init__.py`

**修改前：**
```python
"""
API 路由模块（简化版）
"""

from . import sessions, tasks, tools, websocket

__all__ = ["sessions", "tasks", "tools", "websocket"]
```

**修改后：**
```python
"""
API 路由模块（简化版）
"""

from . import mcp_router, sessions, tasks, tools, websocket

__all__ = ["sessions", "tasks", "tools", "websocket", "mcp_router"]
```

### 2. 修改 `web_api/main.py`

**导入部分修改前：**
```python
from .routers import agent_detail, sessions, tasks, tools, websocket
```

**导入部分修改后：**
```python
from .routers import agent_detail, mcp_router, sessions, tasks, tools, websocket
```

**路由注册部分修改前：**
```python
# 路由注册
app.include_router(sessions.router, prefix="/api")
app.include_router(tasks.router, prefix="/api")
app.include_router(tools.router, prefix="/api")
app.include_router(agent_detail.router, prefix="/api")
app.include_router(websocket.router, prefix="/ws")
```

**路由注册部分修改后：**
```python
# 路由注册
app.include_router(sessions.router, prefix="/api")
app.include_router(tasks.router, prefix="/api")
app.include_router(tools.router, prefix="/api")
app.include_router(agent_detail.router, prefix="/api")
app.include_router(mcp_router.router)  # MCP路由已经包含了/api/mcp前缀
app.include_router(websocket.router, prefix="/ws")
```

## 修复结果

修复后，以下 MCP 接口应该可以正常访问：

- `GET /api/mcp/servers` - 获取MCP服务器列表
- `GET /api/mcp/servers/{server_id}` - 获取指定服务器信息
- `POST /api/mcp/servers` - 创建新的服务器配置
- `PUT /api/mcp/servers/{server_id}` - 更新服务器配置
- `DELETE /api/mcp/servers/{server_id}` - 删除服务器配置
- `POST /api/mcp/servers/{server_id}/connect` - 连接服务器
- `POST /api/mcp/servers/{server_id}/disconnect` - 断开连接
- `GET /api/mcp/servers/{server_id}/status` - 获取服务器状态

## 验证方法

1. **重启服务**：
   ```bash
   .venv\Scripts\activate  
   python run_web_api.py
   ```

2. **测试接口**：
   ```bash
   # 测试获取服务器列表
   curl -X GET "http://localhost:8000/api/mcp/servers"
   
   # 查看API文档
   # 访问 http://localhost:8000/docs
   ```

3. **检查API文档**：
   - 访问 `http://localhost:8000/docs`
   - 应该能看到 "MCP管理" 标签下的所有接口

## 注意事项

1. **路由前缀**: MCP 路由在 `mcp_router.py` 中已经定义了 `prefix="/api/mcp"`，所以在注册时不需要额外添加前缀
2. **依赖检查**: 确保 `web_api.services.mcp_service` 模块正常工作
3. **配置文件**: MCP 服务依赖 `config/mcp.json` 配置文件，如果不存在会自动创建

## 相关文件

- `web_api/routers/__init__.py` - 路由模块导入
- `web_api/main.py` - 主应用和路由注册
- `web_api/routers/mcp_router.py` - MCP 路由定义
- `web_api/services/mcp_service.py` - MCP 服务逻辑
- `web_api/schemas/mcp.py` - MCP 数据模型

## 修复时间

2025-07-22

## 修复人员

Augment Agent
