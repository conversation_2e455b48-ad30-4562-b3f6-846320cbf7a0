/**
 * 测试延迟CDP连接功能
 */

// 启用调试日志
process.env.DEBUG = 'pw:mcp:cdp-manager';

import { contextFactory } from './external/playwright-mcp/lib/browserContextFactory.js';

async function testDelayedCDP() {
    console.log('🚀 开始测试延迟CDP连接...\n');

    // 模拟浏览器配置
    const browserConfig = {
        browserName: 'chromium',
        launchOptions: {
            channel: 'chrome',
            headless: false,
            chromiumSandbox: false,
            args: ['--no-sandbox']
        },
        contextOptions: {
            viewport: { width: 1280, height: 720 }
        },
        isolated: false,
        cdpEndpoint: 'auto' // 使用auto模式
    };

    try {
        console.log('📋 创建CDP上下文工厂...');
        const factory = contextFactory(browserConfig);
        console.log(`✅ 工厂类型: ${factory.name}`);

        console.log('\n📋 创建浏览器上下文（这时才会解析CDP端点）...');
        const { browserContext, close } = await factory.createContext();
        
        console.log('✅ 浏览器上下文创建成功!');
        console.log(`浏览器: ${browserContext.browser().browserType().name()}`);

        // 测试基本功能
        console.log('\n📋 测试基本功能...');
        const page = await browserContext.newPage();
        await page.goto('https://www.example.com');
        console.log(`✅ 页面标题: ${await page.title()}`);

        // 清理
        await page.close();
        await close();
        console.log('\n🎉 测试完成！');

    } catch (error) {
        console.error('❌ 测试失败:', error.message);
        console.error('详细错误:', error);
    }
}

testDelayedCDP().catch(console.error);
