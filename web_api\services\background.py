"""
后台任务执行器

负责从队列中获取任务并执行
"""

import asyncio
import subprocess
from datetime import datetime

from app.agent.manus import Manus
from app.logger import logger
from .state_manager import state_manager
from .agent_adapter import SimpleAgentAdapter


# 全局 Agent 适配器实例
agent_adapter = SimpleAgentAdapter(state_manager)

# 全局 Manus 实例（用于预初始化 MCP 连接）
_global_manus_instance = None


async def task_executor():
    """后台任务执行器"""
    logger.info("启动后台任务执行器")

    while True:
        try:
            # 检查所有会话的任务队列
            for session_id, queue in state_manager.task_queues.items():
                try:
                    # 非阻塞检查队列
                    task_id = queue.get_nowait()

                    # 启动任务执行
                    task = asyncio.create_task(
                        agent_adapter.execute_task(task_id)
                    )
                    agent_adapter.running_tasks[task_id] = task

                    logger.info(f"启动任务执行: {task_id}")

                except asyncio.QueueEmpty:
                    continue
                except Exception as e:
                    logger.error(f"任务队列处理错误: {e}")

        except Exception as e:
            logger.error(f"任务执行器错误: {e}")

        await asyncio.sleep(0.1)  # 避免CPU占用过高


async def cleanup_task():
    """定期清理过期数据"""
    logger.info("启动数据清理任务")

    while True:
        try:
            await asyncio.sleep(3600)  # 每小时清理一次
            await state_manager.cleanup_expired()
            logger.info("完成过期数据清理")
        except Exception as e:
            logger.error(f"数据清理错误: {e}")


async def initialize_manus_with_mcp():
    """初始化 Manus 实例并连接 MCP 服务器"""
    global _global_manus_instance

    try:
        logger.info("🚀 初始化 Manus 实例并连接 MCP 服务器...")

        # 创建 Manus 实例（会自动连接配置文件中的 MCP 服务器）
        _global_manus_instance = await Manus.create()

        # 检查连接状态
        if _global_manus_instance.mcp_clients.sessions:
            available_tools = list(_global_manus_instance.mcp_clients.tool_map.keys())
            playwright_tools = [
                tool for tool in available_tools if "playwright" in tool.lower() or "browser" in tool.lower()
            ]

            logger.info(f"✅ 成功连接到 {len(_global_manus_instance.mcp_clients.sessions)} 个 MCP 服务器！")
            logger.info(f"📋 发现 {len(available_tools)} 个工具，其中 {len(playwright_tools)} 个 Playwright/浏览器工具")

            if playwright_tools:
                logger.info("🎭 Playwright MCP 集成成功，可用工具包括:")
                for tool in playwright_tools[:5]:  # 只显示前5个
                    logger.info(f"   - {tool}")
                if len(playwright_tools) > 5:
                    logger.info(f"   - ... 还有 {len(playwright_tools) - 5} 个工具")
        else:
            logger.warning("⚠️ 未检测到 MCP 服务器连接")

    except Exception as e:
        logger.error(f"❌ 初始化 Manus 和 MCP 连接时出错: {str(e)}")
        _global_manus_instance = None


def get_global_manus_instance():
    """获取全局 Manus 实例"""
    return _global_manus_instance


async def start_background_tasks():
    """启动所有后台任务"""
    logger.info("启动后台服务")

    # 初始化状态管理器（处理重新排队）
    await state_manager.initialize_after_startup()

    # 初始化 Manus 实例并连接 MCP 服务器
    await initialize_manus_with_mcp()

    # 创建后台任务
    tasks = [
        asyncio.create_task(task_executor()),
        asyncio.create_task(cleanup_task())
    ]

    logger.info("后台任务已启动")
    return tasks
