"""
会话管理路由

提供会话的创建、查询等 REST API
"""

from fastapi import APIRouter, HTTPException, status
from typing import List

from ..schemas.session import SessionCreate, SessionResponse, SessionList
from ..services.state_manager import state_manager

router = APIRouter(prefix="/sessions", tags=["sessions"])


@router.post("", response_model=SessionResponse, status_code=status.HTTP_201_CREATED)
async def create_session():
    """创建新的会话"""
    try:
        session_id = await state_manager.create_session()
        session = state_manager.get_session(session_id)

        if not session:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="会话创建失败"
            )

        return SessionResponse(
            session_id=session["session_id"],
            status=session["status"],
            created_at=session["created_at"],
            task_count=len(session["task_ids"])
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"创建会话失败: {str(e)}"
        )


@router.get("/{session_id}", response_model=SessionResponse)
async def get_session(session_id: str):
    """获取指定会话的信息"""
    session = state_manager.get_session(session_id)

    if not session:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"会话 {session_id} 不存在"
        )

    return SessionResponse(
        session_id=session["session_id"],
        status=session["status"],
        created_at=session["created_at"],
        task_count=len(session["task_ids"])
    )


@router.get("", response_model=SessionList)
async def list_sessions():
    """获取所有会话列表"""
    sessions = state_manager.list_sessions()

    session_responses = [
        SessionResponse(
            session_id=session["session_id"],
            status=session["status"],
            created_at=session["created_at"],
            task_count=len(session["task_ids"])
        )
        for session in sessions
    ]

    return SessionList(
        sessions=session_responses,
        total=len(session_responses)
    )
