/**
 * 直接测试浏览器启动
 */

import { spawn } from 'node:child_process';
import fs from 'node:fs';

async function testBrowserLaunch() {
    console.log('🚀 开始测试浏览器启动...\n');

    // 浏览器路径
    const executablePath = 'C:\\Program Files\\Google\\Chrome\\Application\\chrome.exe';
    
    // 检查文件是否存在
    console.log('📋 检查浏览器可执行文件...');
    if (fs.existsSync(executablePath)) {
        console.log(`✅ 浏览器文件存在: ${executablePath}`);
    } else {
        console.log(`❌ 浏览器文件不存在: ${executablePath}`);
        return;
    }

    // 找一个可用端口
    const port = 9333; // 使用一个不常用的端口
    const endpoint = `http://localhost:${port}`;

    // 启动参数
    const args = [
        `--remote-debugging-port=${port}`,
        '--no-first-run',
        '--no-default-browser-check',
        '--disable-background-timer-throttling',
        '--disable-backgrounding-occluded-windows',
        '--disable-renderer-backgrounding',
        '--disable-features=TranslateUI',
        '--disable-ipc-flooding-protection',
        '--no-sandbox',
        '--disable-web-security'
    ];

    console.log('📋 启动浏览器...');
    console.log(`命令: ${executablePath}`);
    console.log(`参数: ${args.join(' ')}`);

    // 启动浏览器
    const browserProcess = spawn(executablePath, args, {
        detached: true,
        stdio: ['ignore', 'pipe', 'pipe']
    });

    // 监听输出
    browserProcess.stdout?.on('data', (data) => {
        console.log(`stdout: ${data.toString()}`);
    });

    browserProcess.stderr?.on('data', (data) => {
        console.log(`stderr: ${data.toString()}`);
    });

    browserProcess.on('error', (error) => {
        console.log(`❌ 进程错误: ${error.message}`);
    });

    browserProcess.on('exit', (code, signal) => {
        console.log(`进程退出: code=${code}, signal=${signal}`);
    });

    browserProcess.unref();

    // 等待浏览器启动
    console.log('📋 等待浏览器启动...');
    
    for (let i = 0; i < 30; i++) {
        try {
            console.log(`尝试连接 (第${i+1}次): ${endpoint}/json/version`);
            
            const response = await fetch(`${endpoint}/json/version`, {
                signal: AbortSignal.timeout(3000)
            });
            
            if (response.ok) {
                const version = await response.json();
                console.log(`✅ 浏览器已就绪!`);
                console.log(`浏览器信息:`, JSON.stringify(version, null, 2));
                return;
            } else {
                console.log(`响应状态: ${response.status}`);
            }
        } catch (error) {
            console.log(`连接失败: ${error.message}`);
        }
        
        // 等待1秒
        await new Promise(resolve => setTimeout(resolve, 1000));
    }
    
    console.log('❌ 浏览器启动超时');
}

testBrowserLaunch().catch(console.error);
