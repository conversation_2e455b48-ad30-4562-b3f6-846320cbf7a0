# Configuration Management Guide

## Configuration Files

- **Primary Config**: [config/config.toml](mdc:config/config.toml) - Main configuration file
- **Config Module**: [app/config.py](mdc:app/config.py) - Configuration loading and validation

## LLM Configuration

The project supports multiple LLM providers. Configure in `[llm]` section of config.toml:

### Supported Providers

- **Google Gemini** (currently active): `gemini-2.5-pro`
- **OpenAI**: Various GPT models
- **Anthropic Claude**: Claude models
- **Azure OpenAI**: Azure-hosted models
- **Ollama**: Local models
- **DashScope**: Alibaba's Qwen models
- **AWS Bedrock**: Bedrock-hosted models

### Configuration Structure

```toml
[llm]
model = "model-name"
base_url = "api-endpoint"
api_key = "your-api-key"
temperature = 0.8
max_tokens = 8096
api_type = "provider-type"  # For specific providers
```

## Other Configuration Sections

- `[browser]` - Browser automation settings
- `[search]` - Search engine configuration
- `[mcp]` - Model Context Protocol settings
- `[runflow]` - Workflow agent settings
- `[sandbox]` - Sandbox environment settings

## Environment Variables

Configuration can be overridden with environment variables following the pattern:
`OPENMANUS_SECTION_KEY` (e.g., `OPENMANUS_LLM_API_KEY`)

## Configuration Loading

Configuration is loaded and validated through Pydantic models in [app/config.py](mdc:app/config.py). Always use the `config` object to access settings throughout the application.
description:
globs:
alwaysApply: false

---
