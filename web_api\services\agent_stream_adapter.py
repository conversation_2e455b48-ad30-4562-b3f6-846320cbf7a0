"""
Agent流式适配器

用于拦截Agent执行过程并实时发送事件到WebSocket
"""

import asyncio
from typing import Optional, Any, Dict
from functools import wraps

from app.agent.base import BaseAgent
from app.agent.toolcall import ToolCallAgent
from app.logger import logger
from .agent_event_emitter import agent_event_emitter


class AgentStreamAdapter:
    """Agent流式适配器，用于拦截Agent执行过程"""
    
    def __init__(self, agent: BaseAgent, session_id: str):
        self.agent = agent
        self.session_id = session_id
        self.original_methods = {}
        self._setup_interceptors()
    
    def _setup_interceptors(self):
        """设置方法拦截器"""
        # 拦截run方法
        self.original_methods['run'] = self.agent.run
        self.agent.run = self._intercept_run
        
        # 拦截step方法
        self.original_methods['step'] = self.agent.step
        self.agent.step = self._intercept_step
        
        # 如果是ToolCallAgent，拦截think和act方法
        if isinstance(self.agent, ToolCallAgent):
            self.original_methods['think'] = self.agent.think
            self.agent.think = self._intercept_think
            
            self.original_methods['act'] = self.agent.act
            self.agent.act = self._intercept_act
            
            self.original_methods['execute_tool'] = self.agent.execute_tool
            self.agent.execute_tool = self._intercept_execute_tool
    
    def restore_methods(self):
        """恢复原始方法"""
        for method_name, original_method in self.original_methods.items():
            setattr(self.agent, method_name, original_method)
        self.original_methods.clear()
    
    async def _intercept_run(self, request: Optional[str] = None) -> str:
        """拦截run方法"""
        try:
            # 发送Agent开始事件
            agent_event_emitter.emit_agent_start(
                session_id=self.session_id,
                step=0,
                prompt=request or ""
            )
            
            # 调用原始方法
            result = await self.original_methods['run'](request)
            
            # 发送Agent完成事件
            agent_event_emitter.emit_agent_finish(
                session_id=self.session_id,
                step=self.agent.current_step,
                result=result
            )
            
            return result
            
        except Exception as e:
            # 发送错误事件
            agent_event_emitter.emit_error(
                session_id=self.session_id,
                step=self.agent.current_step,
                error=str(e)
            )
            raise
    
    async def _intercept_step(self) -> str:
        """拦截step方法"""
        try:
            # 发送步骤开始事件
            agent_event_emitter.emit_agent_step(
                session_id=self.session_id,
                step=self.agent.current_step,
                max_steps=self.agent.max_steps
            )
            
            # 调用原始方法
            result = await self.original_methods['step']()
            
            return result
            
        except Exception as e:
            # 发送错误事件
            agent_event_emitter.emit_error(
                session_id=self.session_id,
                step=self.agent.current_step,
                error=str(e)
            )
            raise
    
    async def _intercept_think(self) -> bool:
        """拦截think方法"""
        try:
            # 调用原始方法
            result = await self.original_methods['think']()
            
            # 获取思考内容和工具选择
            if hasattr(self.agent, 'memory') and self.agent.memory.messages:
                last_message = self.agent.memory.messages[-1]
                if hasattr(last_message, 'content') and last_message.content:
                    # 发送思考事件
                    agent_event_emitter.emit_agent_thinking(
                        session_id=self.session_id,
                        step=self.agent.current_step,
                        thoughts=last_message.content
                    )
            
            # 如果有工具调用，发送工具选择事件
            if hasattr(self.agent, 'tool_calls') and self.agent.tool_calls:
                tools = [call.function.name for call in self.agent.tool_calls]
                arguments = self.agent.tool_calls[0].function.arguments if self.agent.tool_calls else ""
                
                agent_event_emitter.emit_tool_selection(
                    session_id=self.session_id,
                    step=self.agent.current_step,
                    tools=tools,
                    arguments=arguments
                )
            
            return result
            
        except Exception as e:
            # 发送错误事件
            agent_event_emitter.emit_error(
                session_id=self.session_id,
                step=self.agent.current_step,
                error=str(e)
            )
            raise
    
    async def _intercept_act(self) -> str:
        """拦截act方法"""
        try:
            # 调用原始方法
            result = await self.original_methods['act']()
            
            return result
            
        except Exception as e:
            # 发送错误事件
            agent_event_emitter.emit_error(
                session_id=self.session_id,
                step=self.agent.current_step,
                error=str(e)
            )
            raise
    
    async def _intercept_execute_tool(self, command) -> str:
        """拦截execute_tool方法"""
        try:
            tool_name = command.function.name if command and command.function else "unknown"
            
            # 发送工具执行事件
            agent_event_emitter.emit_tool_execution(
                session_id=self.session_id,
                step=self.agent.current_step,
                tool_name=tool_name
            )
            
            # 调用原始方法
            result = await self.original_methods['execute_tool'](command)
            
            # 发送工具结果事件
            agent_event_emitter.emit_tool_result(
                session_id=self.session_id,
                step=self.agent.current_step,
                tool_name=tool_name,
                result=result
            )
            
            # 检查是否是文件操作
            self._check_file_operation(tool_name, command, result)
            
            return result
            
        except Exception as e:
            # 发送错误事件
            agent_event_emitter.emit_error(
                session_id=self.session_id,
                step=self.agent.current_step,
                error=str(e)
            )
            raise
    
    def _check_file_operation(self, tool_name: str, command, result: str):
        """检查是否是文件操作并发送相应事件"""
        file_tools = {
            'str-replace-editor': 'edit',
            'save-file': 'create',
            'remove-files': 'delete',
            'view': 'read'
        }
        
        if tool_name in file_tools:
            try:
                import json
                args = json.loads(command.function.arguments or "{}")
                file_path = args.get('path', '') or args.get('file_paths', [''])[0]
                content = args.get('file_content', '') or args.get('new_str_1', '')
                
                agent_event_emitter.emit_file_operation(
                    session_id=self.session_id,
                    step=self.agent.current_step,
                    operation=file_tools[tool_name],
                    file_path=file_path,
                    content=content
                )
            except Exception as e:
                logger.debug(f"无法解析文件操作参数: {e}")


def create_streaming_agent(agent: BaseAgent, session_id: str) -> AgentStreamAdapter:
    """创建流式Agent适配器"""
    return AgentStreamAdapter(agent, session_id)
