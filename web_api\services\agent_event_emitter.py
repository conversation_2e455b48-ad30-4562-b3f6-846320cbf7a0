"""
Agent事件发射器

用于捕获Agent执行过程中的关键事件并实时推送到WebSocket
"""

import asyncio
from datetime import datetime
from enum import Enum
from typing import Dict, List, Optional, Any, Callable
from dataclasses import dataclass
import json

from app.logger import logger


class AgentEventType(Enum):
    """Agent事件类型"""
    AGENT_START = "agent_start"
    AGENT_STEP = "agent_step"
    AGENT_THINKING = "agent_thinking"
    TOOL_SELECTION = "tool_selection"
    TOOL_EXECUTION = "tool_execution"
    TOOL_RESULT = "tool_result"
    FILE_OPERATION = "file_operation"
    ERROR = "error"
    AGENT_FINISH = "agent_finish"


@dataclass
class AgentEvent:
    """Agent事件数据结构"""
    event_type: AgentEventType
    timestamp: datetime
    session_id: str
    step: int
    data: Dict[str, Any]
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            "event_type": self.event_type.value,
            "timestamp": self.timestamp.isoformat(),
            "session_id": self.session_id,
            "step": self.step,
            "data": self.data
        }


class AgentEventEmitter:
    """Agent事件发射器"""
    
    def __init__(self):
        self._listeners: Dict[str, List[Callable]] = {}
        self._session_events: Dict[str, List[AgentEvent]] = {}
    
    def add_listener(self, session_id: str, callback: Callable[[AgentEvent], None]):
        """添加事件监听器"""
        if session_id not in self._listeners:
            self._listeners[session_id] = []
        self._listeners[session_id].append(callback)
        logger.debug(f"为会话 {session_id} 添加事件监听器")
    
    def remove_listener(self, session_id: str, callback: Callable[[AgentEvent], None]):
        """移除事件监听器"""
        if session_id in self._listeners:
            try:
                self._listeners[session_id].remove(callback)
                if not self._listeners[session_id]:
                    del self._listeners[session_id]
                logger.debug(f"为会话 {session_id} 移除事件监听器")
            except ValueError:
                pass
    
    def emit_event(self, event: AgentEvent):
        """发射事件"""
        # 存储事件历史
        if event.session_id not in self._session_events:
            self._session_events[event.session_id] = []
        self._session_events[event.session_id].append(event)
        
        # 通知监听器
        if event.session_id in self._listeners:
            for callback in self._listeners[event.session_id]:
                try:
                    if asyncio.iscoroutinefunction(callback):
                        asyncio.create_task(callback(event))
                    else:
                        callback(event)
                except Exception as e:
                    logger.error(f"事件监听器执行错误: {e}")
    
    def emit_agent_start(self, session_id: str, step: int, prompt: str):
        """发射Agent开始事件"""
        event = AgentEvent(
            event_type=AgentEventType.AGENT_START,
            timestamp=datetime.now(),
            session_id=session_id,
            step=step,
            data={"prompt": prompt}
        )
        self.emit_event(event)
    
    def emit_agent_step(self, session_id: str, step: int, max_steps: int):
        """发射Agent步骤事件"""
        event = AgentEvent(
            event_type=AgentEventType.AGENT_STEP,
            timestamp=datetime.now(),
            session_id=session_id,
            step=step,
            data={"max_steps": max_steps}
        )
        self.emit_event(event)
    
    def emit_agent_thinking(self, session_id: str, step: int, thoughts: str):
        """发射Agent思考事件"""
        event = AgentEvent(
            event_type=AgentEventType.AGENT_THINKING,
            timestamp=datetime.now(),
            session_id=session_id,
            step=step,
            data={"thoughts": thoughts}
        )
        self.emit_event(event)
    
    def emit_tool_selection(self, session_id: str, step: int, tools: List[str], arguments: str):
        """发射工具选择事件"""
        event = AgentEvent(
            event_type=AgentEventType.TOOL_SELECTION,
            timestamp=datetime.now(),
            session_id=session_id,
            step=step,
            data={"tools": tools, "arguments": arguments}
        )
        self.emit_event(event)
    
    def emit_tool_execution(self, session_id: str, step: int, tool_name: str):
        """发射工具执行事件"""
        event = AgentEvent(
            event_type=AgentEventType.TOOL_EXECUTION,
            timestamp=datetime.now(),
            session_id=session_id,
            step=step,
            data={"tool_name": tool_name}
        )
        self.emit_event(event)
    
    def emit_tool_result(self, session_id: str, step: int, tool_name: str, result: str):
        """发射工具结果事件"""
        event = AgentEvent(
            event_type=AgentEventType.TOOL_RESULT,
            timestamp=datetime.now(),
            session_id=session_id,
            step=step,
            data={"tool_name": tool_name, "result": result}
        )
        self.emit_event(event)
    
    def emit_file_operation(self, session_id: str, step: int, operation: str, file_path: str, content: Optional[str] = None):
        """发射文件操作事件"""
        event = AgentEvent(
            event_type=AgentEventType.FILE_OPERATION,
            timestamp=datetime.now(),
            session_id=session_id,
            step=step,
            data={
                "operation": operation,
                "file_path": file_path,
                "content": content[:500] + "..." if content and len(content) > 500 else content
            }
        )
        self.emit_event(event)
    
    def emit_error(self, session_id: str, step: int, error: str):
        """发射错误事件"""
        event = AgentEvent(
            event_type=AgentEventType.ERROR,
            timestamp=datetime.now(),
            session_id=session_id,
            step=step,
            data={"error": error}
        )
        self.emit_event(event)
    
    def emit_agent_finish(self, session_id: str, step: int, result: str):
        """发射Agent完成事件"""
        event = AgentEvent(
            event_type=AgentEventType.AGENT_FINISH,
            timestamp=datetime.now(),
            session_id=session_id,
            step=step,
            data={"result": result}
        )
        self.emit_event(event)
    
    def get_session_events(self, session_id: str) -> List[AgentEvent]:
        """获取会话的所有事件"""
        return self._session_events.get(session_id, [])
    
    def clear_session_events(self, session_id: str):
        """清理会话事件"""
        if session_id in self._session_events:
            del self._session_events[session_id]
        if session_id in self._listeners:
            del self._listeners[session_id]
        logger.debug(f"清理会话 {session_id} 的事件数据")


# 全局事件发射器实例
agent_event_emitter = AgentEventEmitter()
