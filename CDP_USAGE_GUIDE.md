# CDP智能连接使用指南

## 概述

这个增强版的playwright-mcp服务实现了智能CDP（Chrome DevTools Protocol）连接功能，可以自动检测现有浏览器实例或启动新的浏览器实例。

## 功能特性

### 🔍 智能检测
- 自动扫描常用CDP端口（9222, 9223, 9224, 9225）
- 检测现有的Chrome/Chromium/Edge浏览器实例
- 验证浏览器兼容性

### 🚀 自动启动
- 如果没有检测到现有浏览器，自动启动新实例
- 智能选择可用端口
- 支持自定义启动参数

### ⚙️ 灵活配置
- 支持多种CDP端点配置方式
- 兼容现有配置格式
- 支持用户数据目录配置

## 配置方式

### 1. 自动模式（推荐）
```json
{
  "mcpServers": {
    "playwright": {
      "type": "stdio",
      "command": "node",
      "args": [
        "external/playwright-mcp/cli.js",
        "--browser=chrome",
        "--no-sandbox",
        "--cdp-endpoint=auto",
        "--config=playwright-mcp-config.json"
      ]
    }
  }
}
```

### 2. 固定端口模式
```json
{
  "args": [
    "--cdp-endpoint=9222"
  ]
}
```

### 3. 完整URL模式
```json
{
  "args": [
    "--cdp-endpoint=http://localhost:9222"
  ]
}
```

## 工作流程

### 自动模式工作流程
1. **检测阶段**: 扫描常用端口，查找现有浏览器实例
2. **验证阶段**: 检查找到的浏览器是否兼容
3. **连接阶段**: 如果找到兼容浏览器，直接连接
4. **启动阶段**: 如果没有找到，启动新的浏览器实例
5. **等待阶段**: 等待新启动的浏览器就绪

### 检测逻辑
- 向 `http://localhost:{port}/json/version` 发送请求
- 检查响应中的 `product` 字段
- 验证是否包含 "chrome"、"chromium" 或 "edge"

## 配置文件说明

### playwright-mcp-config.json
```json
{
  "browser": {
    "browserName": "chromium",
    "launchOptions": {
      "channel": "chrome",
      "headless": false,
      "chromiumSandbox": false,
      "args": [
        "--no-sandbox",
        "--disable-web-security",
        "--disable-features=VizDisplayCompositor",
        "--disable-background-timer-throttling",
        "--disable-backgrounding-occluded-windows",
        "--disable-renderer-backgrounding",
        "--disable-features=TranslateUI",
        "--disable-ipc-flooding-protection"
      ]
    },
    "contextOptions": {
      "viewport": {
        "width": 1280,
        "height": 720
      }
    },
    "isolated": false
  }
}
```

### 关键配置项说明
- `isolated: false`: 使用现有浏览器上下文，而不是创建新的隔离上下文
- `chromiumSandbox: false`: 禁用沙盒模式，提高兼容性
- `args`: 包含优化CDP连接的启动参数

## 测试和验证

### 运行测试脚本
```bash
node test-cdp-connection.js
```

### 手动验证
1. 启动浏览器并开启远程调试：
   ```bash
   chrome --remote-debugging-port=9222
   ```

2. 验证CDP端点：
   ```bash
   curl http://localhost:9222/json/version
   ```

3. 启动playwright-mcp服务：
   ```bash
   node external/playwright-mcp/cli.js --browser=chrome --no-sandbox --cdp-endpoint=auto --config=playwright-mcp-config.json
   ```

## 故障排除

### 常见问题

#### 1. 端口被占用
- **现象**: 启动失败，提示端口被占用
- **解决**: 系统会自动选择其他可用端口

#### 2. 浏览器启动失败
- **现象**: 无法启动新的浏览器实例
- **解决**: 检查浏览器可执行文件路径，确保Chrome/Chromium已安装

#### 3. 连接超时
- **现象**: 等待浏览器就绪超时
- **解决**: 增加超时时间或检查防火墙设置

### 调试模式
启用调试日志：
```bash
DEBUG=pw:mcp:cdp-manager node external/playwright-mcp/cli.js --cdp-endpoint=auto
```

## 最佳实践

### 1. 生产环境
- 使用固定端口配置，避免端口冲突
- 配置适当的用户数据目录
- 启用必要的安全参数

### 2. 开发环境
- 使用自动模式，提高开发效率
- 启用调试日志，便于问题排查
- 配置合适的视口大小

### 3. 性能优化
- 复用现有浏览器实例，减少启动时间
- 配置合适的启动参数，优化性能
- 避免不必要的浏览器重启

## 兼容性

### 支持的浏览器
- Google Chrome
- Chromium
- Microsoft Edge

### 支持的平台
- Windows
- macOS
- Linux

### Node.js版本要求
- Node.js 16.0+
- 支持ES模块
