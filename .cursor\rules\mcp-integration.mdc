# MCP (Model Context Protocol) Integration Guide

## MCP Overview

Model Context Protocol enables dynamic tool integration and communication between AI agents and external services.

## MCP Components

- **MCP Module**: [app/mcp/](mdc:app/mcp/) - Core MCP implementation
- **MCP Server**: [app/mcp/server.py](mdc:app/mcp/server.py) - MCP server implementation
- **MCP Clients**: [app/tool/mcp.py](mdc:app/tool/mcp.py) - Client management and tool integration
- **MCP Agent**: [app/agent/mcp.py](mdc:app/agent/mcp.py) - MCP-enabled agent

## Configuration

MCP is configured in [config/config.toml](mdc:config/config.toml):

```toml
[mcp]
server_reference = "app.mcp.server"  # Server module reference
```

## Playwright MCP Integration

Special integration with Playwright for browser automation:

- **Config**: [playwright-mcp-config.json](mdc:playwright-mcp-config.json) - Playwright MCP configuration
- **Runner**: [run_with_playwright_mcp.py](mdc:run_with_playwright_mcp.py) - Playwright MCP execution
- **Documentation**:
  - [PLAYWRIGHT_MCP_INTEGRATION.md](mdc:PLAYWRIGHT_MCP_INTEGRATION.md)
  - [PLAYWRIGHT_MCP_CODE_EXAMPLES.md](mdc:PLAYWRIGHT_MCP_CODE_EXAMPLES.md)
  - [PLAYWRIGHT_MCP_INTEGRATION_CHANGES.md](mdc:PLAYWRIGHT_MCP_INTEGRATION_CHANGES.md)

## MCP Tool Implementation

MCP tools follow the protocol specification:

```python
class MCPClientTool(BaseTool):
    # Implements MCP tool interface
    # Handles protocol communication
    # Manages tool discovery and execution
```

## MCP Server Setup

1. Configure server reference in config.toml
2. Implement server in `app.mcp.server` module
3. Register available tools and capabilities
4. Handle client connections and requests

## Usage Patterns

- Use `MCPClients` to manage multiple MCP connections
- Register MCP tools through `MCPClientTool`
- Configure servers in the MCP configuration section
- Monitor connections and handle failures gracefully

## Best Practices

- Follow MCP protocol specifications
- Implement proper error handling for connection issues
- Use async patterns for MCP communication
- Document available MCP tools and their capabilities
- Test MCP integrations thoroughly
  description:
  globs:
  alwaysApply: false

---
