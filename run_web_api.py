#!/usr/bin/env python
"""
启动简化版Web API服务
"""
import uvicorn
from web_api.main import app
from web_api.config import config

if __name__ == "__main__":
    print(f"🚀 启动RPA Agent简化版API服务")
    print(f"📡 监听地址: {config.host}:{config.port}")
    print(f"🔗 WebSocket: ws://{config.host}:{config.port}/ws/sessions/{{session_id}}")
    print(f"📖 API文档: http://{config.host}:{config.port}/docs")

    uvicorn.run(
        "web_api.main:app",  # 使用import string来支持reload
        host=config.host,
        port=config.port,
        reload=config.debug,
        log_level="info",
        ws="websockets",  # 明确指定使用websockets库
        ws_max_size=16777216,  # 设置WebSocket最大消息大小
        loop="auto"  # 自动选择事件循环
    )
