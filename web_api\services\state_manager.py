"""
简化的内存状态管理器

用于管理会话、任务和 WebSocket 连接的状态
"""

from typing import Dict, Optional, List
import asyncio
import json
import os
from datetime import datetime, timedelta
import uuid
from pathlib import Path

from app.logger import logger
from app.config import PROJECT_ROOT


class SimpleStateManager:
    """简化的内存状态管理器，支持持久化"""

    def __init__(self):
        self.sessions: Dict[str, dict] = {}
        self.tasks: Dict[str, dict] = {}
        self.websockets: Dict[str, list] = {}  # session_id -> [websocket]
        self.task_queues: Dict[str, asyncio.Queue] = {}

        # 重新排队标记
        self._pending_requeue = False

        # 持久化文件路径
        self.data_dir = PROJECT_ROOT / "datas"
        self.sessions_file = self.data_dir / "sessions.json"
        self.tasks_file = self.data_dir / "tasks.json"

        # 确保数据目录存在
        self.data_dir.mkdir(exist_ok=True)

        # 启动时加载数据
        self._load_data()

    def _load_data(self):
        """从文件加载持久化数据"""
        try:
            # 加载会话数据
            if self.sessions_file.exists():
                with open(self.sessions_file, 'r', encoding='utf-8') as f:
                    sessions_data = json.load(f)
                    # 转换时间字符串为datetime对象
                    for session_id, session in sessions_data.items():
                        session['created_at'] = datetime.fromisoformat(session['created_at'])
                        self.sessions[session_id] = session
                        # 重建WebSocket和任务队列容器
                        self.websockets[session_id] = []
                        self.task_queues[session_id] = asyncio.Queue()

                logger.info(f"加载了 {len(self.sessions)} 个会话")

            # 加载任务数据
            if self.tasks_file.exists():
                with open(self.tasks_file, 'r', encoding='utf-8') as f:
                    tasks_data = json.load(f)
                    # 转换时间字符串为datetime对象
                    for task_id, task in tasks_data.items():
                        task['created_at'] = datetime.fromisoformat(task['created_at'])
                        if task.get('start_time'):
                            task['start_time'] = datetime.fromisoformat(task['start_time'])
                        if task.get('end_time'):
                            task['end_time'] = datetime.fromisoformat(task['end_time'])
                        self.tasks[task_id] = task

                logger.info(f"加载了 {len(self.tasks)} 个任务")

                # 记录需要重新排队的任务（稍后异步处理）
                self._pending_requeue = True

        except Exception as e:
            logger.error(f"加载持久化数据失败: {e}")

    async def initialize_after_startup(self):
        """启动后初始化，处理重新排队的任务"""
        if self._pending_requeue:
            await self._requeue_pending_tasks()
            self._pending_requeue = False

    async def _requeue_pending_tasks(self):
        """重新排队未完成的任务"""
        try:
            requeued_count = 0
            for task_id, task in self.tasks.items():
                if task['status'] in ['queued', 'running']:
                    # 重置为排队状态
                    task['status'] = 'queued'
                    task.pop('start_time', None)

                    # 重新加入队列
                    session_id = task['session_id']
                    if session_id in self.task_queues:
                        await self.task_queues[session_id].put(task_id)
                        requeued_count += 1

            if requeued_count > 0:
                logger.info(f"重新排队了 {requeued_count} 个未完成的任务")
                self._save_data()  # 保存更新后的状态

        except Exception as e:
            logger.error(f"重新排队任务失败: {e}")

    def _save_data(self):
        """保存数据到文件"""
        try:
            # 保存会话数据
            sessions_data = {}
            for session_id, session in self.sessions.items():
                session_copy = session.copy()
                session_copy['created_at'] = session_copy['created_at'].isoformat()
                sessions_data[session_id] = session_copy

            with open(self.sessions_file, 'w', encoding='utf-8') as f:
                json.dump(sessions_data, f, ensure_ascii=False, indent=2)

            # 保存任务数据
            tasks_data = {}
            for task_id, task in self.tasks.items():
                task_copy = task.copy()
                task_copy['created_at'] = task_copy['created_at'].isoformat()
                if task_copy.get('start_time'):
                    task_copy['start_time'] = task_copy['start_time'].isoformat()
                if task_copy.get('end_time'):
                    task_copy['end_time'] = task_copy['end_time'].isoformat()
                tasks_data[task_id] = task_copy

            with open(self.tasks_file, 'w', encoding='utf-8') as f:
                json.dump(tasks_data, f, ensure_ascii=False, indent=2)

        except Exception as e:
            logger.error(f"保存持久化数据失败: {e}")

    # 会话管理
    async def create_session(self) -> str:
        """创建新的会话"""
        session_id = str(uuid.uuid4())
        self.sessions[session_id] = {
            "session_id": session_id,
            "status": "active",
            "created_at": datetime.now(),
            "task_ids": []
        }
        self.websockets[session_id] = []
        self.task_queues[session_id] = asyncio.Queue()

        # 持久化保存
        self._save_data()

        logger.info(f"创建会话: {session_id}")
        return session_id

    def get_session(self, session_id: str) -> Optional[dict]:
        """获取会话信息"""
        return self.sessions.get(session_id)

    def list_sessions(self) -> List[dict]:
        """获取所有会话列表"""
        return list(self.sessions.values())

    # 任务管理
    async def create_task(self, session_id: str, message: str) -> str:
        """在指定会话中创建任务"""
        if session_id not in self.sessions:
            raise ValueError(f"会话 {session_id} 不存在")

        task_id = str(uuid.uuid4())
        task = {
            "task_id": task_id,
            "session_id": session_id,
            "message": message,
            "status": "queued",
            "current_step": 0,
            "max_steps": 20,
            "created_at": datetime.now(),
            "result": None,
            "error_message": None
        }
        self.tasks[task_id] = task
        self.sessions[session_id]["task_ids"].append(task_id)

        # 添加到执行队列
        await self.task_queues[session_id].put(task_id)

        # 持久化保存
        self._save_data()

        logger.info(f"创建任务 {task_id} 在会话 {session_id}")
        return task_id

    def get_task(self, task_id: str) -> Optional[dict]:
        """获取任务信息"""
        return self.tasks.get(task_id)

    def update_task(self, task_id: str, **updates):
        """更新任务状态"""
        if task_id in self.tasks:
            self.tasks[task_id].update(updates)

            # 如果是重要状态更新，立即持久化
            if 'status' in updates or 'result' in updates or 'error_message' in updates:
                self._save_data()

            logger.debug(f"更新任务 {task_id}: {updates}")

    def get_session_tasks(self, session_id: str) -> List[dict]:
        """获取会话的所有任务"""
        if session_id not in self.sessions:
            return []
        task_ids = self.sessions[session_id]["task_ids"]
        return [self.tasks[task_id] for task_id in task_ids if task_id in self.tasks]

    def cancel_task(self, task_id: str) -> bool:
        """取消任务"""
        if task_id in self.tasks:
            task = self.tasks[task_id]
            if task["status"] in ["queued", "running"]:
                self.update_task(task_id, status="cancelled")
                logger.info(f"取消任务: {task_id}")
                return True
        return False

    # WebSocket管理
    def add_websocket(self, session_id: str, websocket):
        """添加 WebSocket 连接"""
        if session_id in self.websockets:
            self.websockets[session_id].append(websocket)
            logger.debug(f"添加 WebSocket 连接到会话 {session_id}")

    def remove_websocket(self, session_id: str, websocket):
        """移除 WebSocket 连接"""
        if session_id in self.websockets:
            try:
                self.websockets[session_id].remove(websocket)
                logger.debug(f"移除 WebSocket 连接从会话 {session_id}")
            except ValueError:
                pass

    async def broadcast_to_session(self, session_id: str, message: dict):
        """向会话的所有WebSocket连接广播消息"""
        if session_id in self.websockets:
            dead_connections = []
            for ws in self.websockets[session_id]:
                try:
                    await ws.send_json(message)
                except Exception as e:
                    logger.warning(f"WebSocket 发送失败: {e}")
                    dead_connections.append(ws)

            # 清理断开的连接
            for ws in dead_connections:
                self.remove_websocket(session_id, ws)

    # 清理过期数据
    async def cleanup_expired(self, max_age_hours: int = 24):
        """清理过期的会话和任务"""
        cutoff = datetime.now() - timedelta(hours=max_age_hours)
        expired_sessions = []

        for session_id, session in self.sessions.items():
            if session["created_at"] < cutoff:
                expired_sessions.append(session_id)

        for session_id in expired_sessions:
            # 清理会话相关的所有数据
            if session_id in self.sessions:
                task_ids = self.sessions[session_id]["task_ids"]
                for task_id in task_ids:
                    self.tasks.pop(task_id, None)

                self.sessions.pop(session_id)
                self.websockets.pop(session_id, None)
                self.task_queues.pop(session_id, None)
                logger.info(f"清理过期会话: {session_id}")

        # 保存清理后的数据
        if expired_sessions:
            self._save_data()

    def get_stats(self) -> dict:
        """获取状态统计信息"""
        active_tasks = sum(1 for task in self.tasks.values() if task["status"] == "running")
        total_connections = sum(len(conns) for conns in self.websockets.values())

        return {
            "sessions": len(self.sessions),
            "tasks": len(self.tasks),
            "active_tasks": active_tasks,
            "websocket_connections": total_connections
        }


# 全局状态管理器实例
state_manager = SimpleStateManager()
