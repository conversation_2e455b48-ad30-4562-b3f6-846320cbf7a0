/**
 * 简化的CDP连接测试
 */

import { CDPManager } from './external/playwright-mcp/lib/cdpManager.js';

async function testSimple() {
    console.log('🚀 开始简化测试...\n');

    const browserConfig = {
        browserName: 'chromium',
        launchOptions: {
            channel: 'chrome',
            headless: false,
            chromiumSandbox: false,
            args: ['--no-sandbox']
        },
        contextOptions: {
            viewport: { width: 1280, height: 720 }
        },
        isolated: false
    };

    const cdpManager = new CDPManager(browserConfig);

    try {
        // 测试浏览器路径检测
        console.log('📋 测试浏览器路径检测...');
        const executablePath = cdpManager._getBrowserExecutablePath();
        console.log(`✅ 浏览器路径: ${executablePath}\n`);

        // 测试端点解析（不启动浏览器）
        console.log('📋 测试端点解析...');
        const fixedEndpoint = await cdpManager.resolveCDPEndpoint('9222');
        console.log(`✅ 固定端口解析: ${fixedEndpoint}`);

        const urlEndpoint = await cdpManager.resolveCDPEndpoint('http://localhost:9222');
        console.log(`✅ URL解析: ${urlEndpoint}\n`);

        // 测试现有浏览器检测（不启动新浏览器）
        console.log('📋 测试现有浏览器检测...');
        const existingBrowser = await cdpManager._detectExistingBrowser();
        if (existingBrowser) {
            console.log(`✅ 检测到现有浏览器: ${existingBrowser}`);
        } else {
            console.log('ℹ️  未检测到现有浏览器实例');
        }

        console.log('\n🎉 简化测试完成！');

    } catch (error) {
        console.error('❌ 测试失败:', error.message);
    }
}

testSimple().catch(console.error);
