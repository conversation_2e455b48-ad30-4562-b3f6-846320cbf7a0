"""
任务路由器
"""
from typing import List
from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session

from web_api.dependencies import get_database_session
from web_api.models import Task
from web_api.schemas.task import TaskResponse, TaskCreate

router = APIRouter(
    prefix="/tasks",
    tags=["tasks"],
)


@router.get("/", response_model=List[TaskResponse])
async def get_tasks(
    skip: int = 0,
    limit: int = 100,
    db: Session = Depends(get_database_session)
):
    """
    获取任务列表
    """
    tasks = db.query(Task).filter(Task.is_active == True).offset(skip).limit(limit).all()
    return tasks


@router.post("/", response_model=TaskResponse)
async def create_task(
    task: TaskCreate,
    db: Session = Depends(get_database_session)
):
    """
    创建新任务
    """
    db_task = Task(**task.dict())
    db.add(db_task)
    db.commit()
    db.refresh(db_task)
    return db_task


@router.get("/{task_id}", response_model=TaskResponse)
async def get_task(
    task_id: int,
    db: Session = Depends(get_database_session)
):
    """
    根据ID获取任务
    """
    task = db.query(Task).filter(Task.id == task_id, Task.is_active == True).first()
    if task is None:
        raise HTTPException(status_code=404, detail="任务未找到")
    return task
