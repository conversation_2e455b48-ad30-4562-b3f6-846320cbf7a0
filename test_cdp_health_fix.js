/**
 * 测试CDP健康检查修复
 */

const { CDPManager } = require('./external/playwright-mcp/lib/cdpManager.js');

async function testCDPHealthFix() {
    console.log('🧪 测试CDP健康检查修复...');
    
    const browserConfig = {
        browserName: 'chromium',
        launchOptions: {
            channel: 'chrome',
            headless: false,
            chromiumSandbox: false
        }
    };
    
    const cdpManager = new CDPManager(browserConfig);
    
    try {
        console.log('📋 测试自动模式 (cdpEndpoint = "auto")...');
        const autoEndpoint = await cdpManager.resolveCDPEndpoint('auto');
        console.log(`✅ 自动解析结果: ${autoEndpoint}`);
        
        // 验证连接
        console.log('📋 验证自动解析的端点...');
        const response = await fetch(`${autoEndpoint}/json/version`);
        if (response.ok) {
            const version = await response.json();
            console.log('✅ 自动端点连接验证成功!');
            console.log(`浏览器: ${version.Browser || version.product}`);
            console.log(`协议版本: ${version['Protocol-Version']}`);
        }
        
        // 测试健康检查
        console.log('📋 测试浏览器健康检查...');
        const isHealthy = await cdpManager._testBrowserHealth(autoEndpoint);
        console.log(`健康状态: ${isHealthy ? '✅ 健康' : '❌ 不健康'}`);
        
        console.log('\n🎉 CDP健康检查修复测试完成！');
        
    } catch (error) {
        console.error('❌ 测试失败:', error.message);
        console.error('详细错误:', error);
    }
}

testCDPHealthFix().catch(console.error);
