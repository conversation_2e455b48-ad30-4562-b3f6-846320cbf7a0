"""
任务服务模块
"""
import uuid
from datetime import datetime
from typing import Dict, List, Optional, Any

from web_api.schemas.task import TaskStatus


class TaskService:
    """任务服务类"""

    def __init__(self):
        """初始化任务服务"""
        # 使用内存存储任务，仅用于演示
        # 实际应用中应该使用数据库
        self.tasks: Dict[str, Dict[str, Any]] = {}

    def create_task(self, instruction: str, parameters: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        创建任务

        Args:
            instruction: 任务指令
            parameters: 任务参数

        Returns:
            任务信息
        """
        # 创建任务 ID
        task_id = str(uuid.uuid4())

        # 创建任务信息
        task_info = {
            "task_id": task_id,
            "status": TaskStatus.PENDING,
            "created_at": datetime.now(),
            "instruction": instruction,
            "parameters": parameters,
            "result": None
        }

        # 存储任务信息
        self.tasks[task_id] = task_info

        return task_info

    def get_task(self, task_id: str) -> Optional[Dict[str, Any]]:
        """
        获取任务信息

        Args:
            task_id: 任务 ID

        Returns:
            任务信息，如果任务不存在则返回 None
        """
        return self.tasks.get(task_id)

    def get_all_tasks(self) -> List[Dict[str, Any]]:
        """
        获取所有任务

        Returns:
            任务列表
        """
        return list(self.tasks.values())

    def update_task_status(self, task_id: str, status: TaskStatus, result: Optional[Dict[str, Any]] = None) -> Optional[Dict[str, Any]]:
        """
        更新任务状态

        Args:
            task_id: 任务 ID
            status: 任务状态
            result: 任务结果

        Returns:
            更新后的任务信息，如果任务不存在则返回 None
        """
        task = self.tasks.get(task_id)
        if task:
            task["status"] = status
            if result is not None:
                task["result"] = result
            return task
        return None

    def delete_task(self, task_id: str) -> bool:
        """
        删除任务

        Args:
            task_id: 任务 ID

        Returns:
            是否删除成功
        """
        if task_id in self.tasks:
            del self.tasks[task_id]
            return True
        return False


# 创建服务实例
task_service = TaskService()
