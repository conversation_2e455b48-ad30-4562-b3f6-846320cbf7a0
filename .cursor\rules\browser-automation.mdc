# Browser Automation Guide

## Browser Integration Components

- **BrowserUseTool**: [app/tool/browser_use_tool.py](mdc:app/tool/browser_use_tool.py) - High-level browser automation
- **PlaywrightTool**: [app/tool/playwright_tool.py](mdc:app/tool/playwright_tool.py) - Playwright integration
- **Browser Agent**: [app/agent/browser.py](mdc:app/agent/browser.py) - Browser-specific agent
- **WebSearch**: [app/tool/web_search.py](mdc:app/tool/web_search.py) - Web search capabilities

## Browser Configuration

Configure browser settings in [config/config.toml](mdc:config/config.toml):

```toml
[browser]
headless = false                    # Run in headless mode
disable_security = true             # Disable security features
extra_chromium_args = []            # Additional browser arguments
chrome_instance_path = ""           # Path to Chrome instance
wss_url = ""                       # WebSocket connection URL
cdp_url = ""                       # Chrome DevTools Protocol URL

[browser.proxy]
server = "http://proxy-server:port" # Proxy server
username = "proxy-username"         # Proxy credentials
password = "proxy-password"
```

## Browser Automation Features

- **Page Navigation**: URL loading, navigation controls
- **Element Interaction**: Click, type, scroll, hover actions
- **Content Extraction**: Text, HTML, screenshot capture
- **Form Handling**: Input filling, form submission
- **JavaScript Execution**: Custom script execution
- **Waiting Strategies**: Element visibility, network idle

## Playwright Integration

The project uses both browser-use and Playwright for automation:

- `browser-use` for high-level automation tasks
- `playwright` for low-level browser control
- Integrated through MCP for extensible browser operations

## Web Search Integration

Multiple search engines supported:

```toml
[search]
engine = "Bing"                                      # Primary search engine
fallback_engines = ["DuckDuckGo", "Baidu", "Google"] # Fallback engines
retry_delay = 60                                     # Retry delay in seconds
max_retries = 3                                      # Maximum retry attempts
lang = "zh"                                          # Language preference
country = "cn"                                       # Country preference
```

## Usage Patterns

```python
# Browser automation through tools
browser_tool = BrowserUseTool()
result = await browser_tool.execute(
    action="navigate",
    url="https://example.com"
)

# Direct Playwright usage
playwright_tool = PlaywrightTool()
result = await playwright_tool.execute(
    action="click",
    selector="button#submit"
)
```

## Best Practices

- Use headless mode for automated tasks
- Implement proper waiting strategies for dynamic content
- Handle network timeouts and retries
- Use appropriate selectors (CSS, XPath)
- Clean up browser resources after use
- Configure proxy settings when needed
- Test browser automation across different environments
  description:
  globs:
  alwaysApply: false

---
