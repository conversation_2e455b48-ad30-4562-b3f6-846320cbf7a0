#!/usr/bin/env python
"""
启动 OpenManus 主应用并同时提供 Web API 服务
"""
import asyncio
import threading
import time
import uvicorn
from pathlib import Path
import sys

# 添加项目根目录到 Python 路径
project_root = Path(__file__).resolve().parent
sys.path.insert(0, str(project_root))

from app.agent.manus import Manus
from app.logger import logger
from web_api.config import config as web_config


class OpenManusWithWebAPI:
    """OpenManus主应用与Web API集成启动器"""
    
    def __init__(self):
        self.manus_instance = None
        self.web_api_thread = None
        self.running = False
    
    def start_web_api(self):
        """在单独线程中启动Web API服务"""
        try:
            logger.info(f"Starting Web API server on {web_config.host}:{web_config.port}")
            uvicorn.run(
                "web_api.main:app",
                host=web_config.host,
                port=web_config.port,
                reload=False,  # 在线程中不使用reload
                log_level="info"
            )
        except Exception as e:
            logger.error(f"Web API server error: {e}")
    
    async def initialize_manus(self):
        """初始化Manus实例"""
        try:
            logger.info("Initializing Manus agent...")
            self.manus_instance = await Manus.create()
            logger.info("Manus agent initialized successfully")
            return True
        except Exception as e:
            logger.error(f"Failed to initialize Manus: {e}")
            return False
    
    async def start(self):
        """启动完整的应用"""
        self.running = True
        
        # 启动Web API服务器在单独线程
        self.web_api_thread = threading.Thread(target=self.start_web_api, daemon=True)
        self.web_api_thread.start()
        
        # 等待Web API启动
        await asyncio.sleep(2)
        logger.info("Web API server started")
        
        # 初始化Manus
        if not await self.initialize_manus():
            logger.error("Failed to initialize Manus, exiting...")
            return
        
        logger.info("OpenManus with Web API is ready!")
        logger.info(f"Web API documentation available at: http://{web_config.host}:{web_config.port}/docs")
        
        # 保持主线程运行
        try:
            while self.running:
                await asyncio.sleep(1)
        except KeyboardInterrupt:
            logger.info("Received shutdown signal")
        finally:
            await self.cleanup()
    
    async def cleanup(self):
        """清理资源"""
        logger.info("Cleaning up resources...")
        self.running = False
        
        # 清理Manus连接
        if self.manus_instance:
            try:
                await self.manus_instance.mcp_clients.disconnect()
                logger.info("Manus MCP connections closed")
            except Exception as e:
                logger.error(f"Error closing Manus connections: {e}")
        
        logger.info("Cleanup completed")


async def main():
    """主函数"""
    app = OpenManusWithWebAPI()
    
    try:
        await app.start()
    except Exception as e:
        logger.error(f"Application error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    print("=" * 60)
    print("🚀 Starting OpenManus with Web API")
    print("=" * 60)
    print(f"📡 Web API will be available at: http://{web_config.host}:{web_config.port}")
    print(f"📚 API Documentation: http://{web_config.host}:{web_config.port}/docs")
    print("=" * 60)
    
    asyncio.run(main())
