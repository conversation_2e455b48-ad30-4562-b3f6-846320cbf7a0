"""
Agent 适配器

包装现有的 Manus Agent，添加详细的事件推送和状态管理
"""

import asyncio
import json
import uuid
from datetime import datetime
from typing import Any, Dict, List, Optional

from app.agent.manus import Manus
from app.logger import logger
from app.schema import Message

from ..schemas.agent import (
    AgentExecutionDetail,
    AgentLogEntry,
    ExecutionStep,
    ThinkingProcess,
    ToolCallInfo,
)
from ..schemas.task import StepStatus, TaskStatus
from .agent_stream_adapter import create_streaming_agent
from .state_manager import SimpleStateManager


class EnhancedAgentWrapper:
    """增强的Agent包装器，用于捕获详细的执行信息"""

    def __init__(
        self,
        agent: Manus,
        task_id: str,
        session_id: str,
        state_manager: SimpleStateManager,
    ):
        self.agent = agent
        self.task_id = task_id
        self.session_id = session_id
        self.state_manager = state_manager

        # 执行状态跟踪
        self.execution_detail = AgentExecutionDetail(
            task_id=task_id,
            agent_type="manus",
            instruction="",
            status=TaskStatus.PENDING,
            steps=[],
        )

        # 当前步骤信息
        self.current_step_info: Optional[ExecutionStep] = None
        self.step_start_time: Optional[datetime] = None

        # 工具调用跟踪
        self.active_tool_calls: Dict[str, ToolCallInfo] = {}

        # 原始方法备份
        self._original_think = agent.think
        self._original_act = agent.act
        self._original_step = agent.step

        # 包装方法
        self._wrap_agent_methods()

    def _wrap_agent_methods(self):
        """包装Agent的关键方法以捕获执行信息"""
        self.agent.think = self._wrapped_think
        self.agent.act = self._wrapped_act
        self.agent.step = self._wrapped_step

    async def _wrapped_think(self) -> bool:
        """包装的思考方法"""
        step_num = self.agent.current_step + 1

        # 开始新步骤
        await self._start_new_step(step_num, "思考中...")

        # 推送思考开始事件
        await self.state_manager.broadcast_to_session(
            self.session_id,
            {
                "type": "thinking",
                "timestamp": datetime.now().isoformat(),
                "data": {
                    "task_id": self.task_id,
                    "step": step_num,
                    "content": f"开始分析步骤 {step_num}...",
                    "reasoning": "正在分析当前情况和下一步行动",
                    "context": {
                        "current_step": step_num,
                        "max_steps": self.agent.max_steps,
                    },
                },
            },
        )

        try:
            # 调用原始思考方法
            result = await self._original_think()

            # 获取思考结果（从agent的消息中提取）
            thinking_content = self._extract_thinking_content()

            # 推送详细思考过程
            await self.state_manager.broadcast_to_session(
                self.session_id,
                {
                    "type": "thinking",
                    "timestamp": datetime.now().isoformat(),
                    "data": {
                        "task_id": self.task_id,
                        "step": step_num,
                        "content": thinking_content,
                        "reasoning": "分析完成，准备执行行动",
                        "decision": "继续执行" if result else "思考完成，无需行动",
                        "context": {
                            "will_act": result,
                            "tool_calls_planned": len(
                                getattr(self.agent, "tool_calls", [])
                            ),
                        },
                    },
                },
            )

            return result

        except Exception as e:
            await self._handle_step_error(f"思考过程出错: {str(e)}")
            raise

    async def _wrapped_act(self) -> str:
        """包装的行动方法"""
        step_num = self.agent.current_step

        # 更新步骤状态为执行中
        if self.current_step_info:
            self.current_step_info.status = StepStatus.RUNNING
            self.current_step_info.action = "执行工具调用"

        # 推送步骤更新
        await self.state_manager.broadcast_to_session(
            self.session_id,
            {
                "type": "step_update",
                "timestamp": datetime.now().isoformat(),
                "data": {
                    "task_id": self.task_id,
                    "step": step_num,
                    "action": "执行工具调用",
                    "status": "running",
                    "tool_name": self._get_planned_tool_name(),
                },
            },
        )

        try:
            # 监控工具调用
            await self._monitor_tool_calls()

            # 调用原始行动方法
            result = await self._original_act()

            # 完成当前步骤
            await self._complete_current_step(result)

            return result

        except Exception as e:
            await self._handle_step_error(f"执行过程出错: {str(e)}")
            raise

    async def _wrapped_step(self) -> str:
        """包装的步骤方法"""
        return await self._original_step()

    async def _start_new_step(self, step_num: int, action: str):
        """开始新的执行步骤"""
        self.step_start_time = datetime.now()

        self.current_step_info = ExecutionStep(
            step=step_num,
            timestamp=self.step_start_time,
            action=action,
            status=StepStatus.RUNNING,
            tool_calls=[],
            observations=[],
        )

        self.execution_detail.steps.append(self.current_step_info)
        self.execution_detail.current_step = step_num

    async def _complete_current_step(self, result: str):
        """完成当前步骤"""
        if self.current_step_info and self.step_start_time:
            self.current_step_info.status = StepStatus.COMPLETED
            self.current_step_info.duration = (
                datetime.now() - self.step_start_time
            ).total_seconds()
            self.current_step_info.observations.append(result)

            # 推送步骤完成事件
            await self.state_manager.broadcast_to_session(
                self.session_id,
                {
                    "type": "step_update",
                    "timestamp": datetime.now().isoformat(),
                    "data": {
                        "task_id": self.task_id,
                        "step": self.current_step_info.step,
                        "action": self.current_step_info.action,
                        "status": "completed",
                        "duration": self.current_step_info.duration,
                        "observations": self.current_step_info.observations,
                    },
                },
            )

    async def _handle_step_error(self, error_msg: str):
        """处理步骤错误"""
        if self.current_step_info:
            self.current_step_info.status = StepStatus.ERROR
            self.current_step_info.error = error_msg
            if self.step_start_time:
                self.current_step_info.duration = (
                    datetime.now() - self.step_start_time
                ).total_seconds()

    def _extract_thinking_content(self) -> str:
        """从agent消息中提取思考内容"""
        try:
            if hasattr(self.agent, "memory") and self.agent.memory.messages:
                last_message = self.agent.memory.messages[-1]
                if hasattr(last_message, "content") and last_message.content:
                    return (
                        last_message.content[:500] + "..."
                        if len(last_message.content) > 500
                        else last_message.content
                    )
        except:
            pass
        return "正在思考下一步行动..."

    def _get_planned_tool_name(self) -> Optional[str]:
        """获取计划执行的工具名称"""
        try:
            if hasattr(self.agent, "tool_calls") and self.agent.tool_calls:
                return self.agent.tool_calls[0].function.name
        except:
            pass
        return None

    async def _monitor_tool_calls(self):
        """监控工具调用过程"""
        if not hasattr(self.agent, "tool_calls") or not self.agent.tool_calls:
            return

        for tool_call in self.agent.tool_calls:
            tool_id = str(uuid.uuid4())
            tool_name = tool_call.function.name

            # 创建工具调用信息
            tool_info = ToolCallInfo(
                tool_name=tool_name,
                tool_id=tool_id,
                arguments=json.loads(tool_call.function.arguments or "{}"),
                timestamp=datetime.now(),
                status="pending",
            )

            self.active_tool_calls[tool_id] = tool_info
            if self.current_step_info:
                self.current_step_info.tool_calls.append(tool_info)

            # 推送工具调用开始事件
            await self.state_manager.broadcast_to_session(
                self.session_id,
                {
                    "type": "tool_call",
                    "timestamp": datetime.now().isoformat(),
                    "data": {
                        "task_id": self.task_id,
                        "step": self.agent.current_step,
                        "tool_name": tool_name,
                        "tool_id": tool_id,
                        "status": "start",
                        "arguments": tool_info.arguments,
                    },
                },
            )


class SimpleAgentAdapter:
    """增强的Agent适配器，添加详细的事件推送"""

    def __init__(self, state_manager: SimpleStateManager):
        self.state_manager = state_manager
        self.running_tasks: Dict[str, asyncio.Task] = {}
        self.agent_wrappers: Dict[str, EnhancedAgentWrapper] = {}

    async def execute_task(self, task_id: str):
        """执行任务并推送详细事件"""
        task = self.state_manager.get_task(task_id)
        if not task:
            logger.error(f"任务 {task_id} 不存在")
            return

        session_id = task["session_id"]
        start_time = datetime.now()

        try:
            # 更新任务状态为运行中
            self.state_manager.update_task(
                task_id,
                status=TaskStatus.RUNNING,
                start_time=start_time,
                current_step=0,
            )

            # 推送任务开始事件
            await self.state_manager.broadcast_to_session(
                session_id,
                {
                    "type": "task_status",
                    "timestamp": start_time.isoformat(),
                    "data": {
                        "task_id": task_id,
                        "status": "running",
                        "current_step": 0,
                        "max_steps": 20,
                        "message": f"开始执行任务: {task['message']}",
                        "progress_percentage": 0.0,
                    },
                },
            )

            # 使用全局 Agent 实例，如果不存在则创建新的
            from .background import get_global_manus_instance
            agent = get_global_manus_instance()
            if agent is None:
                # 后备方案：如果全局实例不可用，创建新的
                logger.warning("全局 Manus 实例不可用，创建临时实例")
                agent = await Manus.create()
            else:
                logger.info("使用预初始化的全局 Manus 实例（包含 MCP 连接）")

            # 创建流式Agent适配器
            stream_adapter = create_streaming_agent(agent, session_id)

            # 创建增强的Agent包装器
            wrapper = EnhancedAgentWrapper(
                agent, task_id, session_id, self.state_manager
            )
            wrapper.execution_detail.instruction = task["message"]
            wrapper.execution_detail.status = TaskStatus.RUNNING
            wrapper.execution_detail.start_time = start_time

            self.agent_wrappers[task_id] = wrapper

            # 推送Agent初始化完成事件
            await self.state_manager.broadcast_to_session(
                session_id,
                {
                    "type": "log",
                    "timestamp": datetime.now().isoformat(),
                    "data": {
                        "task_id": task_id,
                        "level": "INFO",
                        "component": "agent",
                        "message": f"Agent初始化完成，开始执行任务: {task['message']}",
                    },
                },
            )

            # 执行任务
            logger.info(f"开始执行任务 {task_id}: {task['message']}")
            result = await agent.run(task["message"])

            # 计算总执行时间
            end_time = datetime.now()
            total_duration = (end_time - start_time).total_seconds()

            # 更新执行详情
            wrapper.execution_detail.status = TaskStatus.COMPLETED
            wrapper.execution_detail.end_time = end_time
            wrapper.execution_detail.total_duration = total_duration
            wrapper.execution_detail.final_result = result

            # 更新任务完成状态
            final_step = agent.current_step
            self.state_manager.update_task(
                task_id,
                status=TaskStatus.COMPLETED,
                end_time=end_time,
                result=result,
                current_step=final_step,
            )

            # 推送完成事件
            await self.state_manager.broadcast_to_session(
                session_id,
                {
                    "type": "task_status",
                    "timestamp": end_time.isoformat(),
                    "data": {
                        "task_id": task_id,
                        "status": "completed",
                        "current_step": final_step,
                        "max_steps": 20,
                        "result": result,
                        "message": "任务执行完成",
                        "progress_percentage": 100.0,
                    },
                },
            )

            # 推送执行总结
            await self.state_manager.broadcast_to_session(
                session_id,
                {
                    "type": "log",
                    "timestamp": end_time.isoformat(),
                    "data": {
                        "task_id": task_id,
                        "level": "INFO",
                        "component": "system",
                        "message": f"任务执行完成，总耗时: {total_duration:.2f}秒，执行步骤: {final_step}",
                        "context": {
                            "total_duration": total_duration,
                            "total_steps": final_step,
                            "success": True,
                        },
                    },
                },
            )

            logger.info(f"任务 {task_id} 执行完成，耗时: {total_duration:.2f}秒")

        except Exception as e:
            logger.error(f"任务 {task_id} 执行失败: {str(e)}", exc_info=True)

            # 处理错误
            end_time = datetime.now()
            error_duration = (end_time - start_time).total_seconds()

            task_info = self.state_manager.get_task(task_id)
            current_step = task_info.get("current_step", 0) if task_info else 0

            self.state_manager.update_task(
                task_id,
                status=TaskStatus.ERROR,
                end_time=end_time,
                error_message=str(e),
            )

            # 推送错误事件
            await self.state_manager.broadcast_to_session(
                session_id,
                {
                    "type": "task_status",
                    "timestamp": end_time.isoformat(),
                    "data": {
                        "task_id": task_id,
                        "status": "error",
                        "current_step": current_step,
                        "error": str(e),
                        "message": f"任务执行失败: {str(e)}",
                    },
                },
            )

            # 推送错误日志
            await self.state_manager.broadcast_to_session(
                session_id,
                {
                    "type": "log",
                    "timestamp": end_time.isoformat(),
                    "data": {
                        "task_id": task_id,
                        "level": "ERROR",
                        "component": "agent",
                        "message": f"任务执行失败: {str(e)}",
                        "context": {
                            "error_duration": error_duration,
                            "current_step": current_step,
                            "error_type": type(e).__name__,
                        },
                    },
                },
            )

        finally:
            # 清理运行中的任务记录
            if task_id in self.running_tasks:
                self.running_tasks.pop(task_id)

            # 清理Agent包装器
            if task_id in self.agent_wrappers:
                self.agent_wrappers.pop(task_id)

            # 清理 Agent 实例
            if "agent" in locals():
                try:
                    await agent.cleanup()
                except:
                    pass

            # 清理流式适配器
            if "stream_adapter" in locals():
                try:
                    stream_adapter.restore_methods()
                except:
                    pass

    async def get_task_execution_detail(
        self, task_id: str
    ) -> Optional[AgentExecutionDetail]:
        """获取任务的详细执行信息"""
        if task_id in self.agent_wrappers:
            return self.agent_wrappers[task_id].execution_detail
        return None

    async def get_task_logs(self, task_id: str) -> List[AgentLogEntry]:
        """获取任务的执行日志"""
        # 这里可以从日志系统中获取相关日志
        # 目前返回空列表，可以后续扩展
        # TODO: 实现从日志系统获取task_id相关的日志
        return []

    async def cancel_task(self, task_id: str) -> bool:
        """取消正在运行的任务"""
        if task_id in self.running_tasks:
            task = self.running_tasks[task_id]
            task.cancel()

            # 更新任务状态
            self.state_manager.update_task(task_id, status=TaskStatus.CANCELLED)

            # 推送取消事件
            task_info = self.state_manager.get_task(task_id)
            if task_info:
                session_id = task_info["session_id"]
                await self.state_manager.broadcast_to_session(
                    session_id,
                    {
                        "type": "task_status",
                        "timestamp": datetime.now().isoformat(),
                        "data": {
                            "task_id": task_id,
                            "status": "cancelled",
                            "message": "任务已被取消",
                        },
                    },
                )

                # 推送取消日志
                await self.state_manager.broadcast_to_session(
                    session_id,
                    {
                        "type": "log",
                        "timestamp": datetime.now().isoformat(),
                        "data": {
                            "task_id": task_id,
                            "level": "WARNING",
                            "component": "system",
                            "message": "任务被用户取消",
                        },
                    },
                )

            logger.info(f"取消任务: {task_id}")
            return True

        return False

    def get_running_tasks(self) -> Dict[str, str]:
        """获取正在运行的任务列表"""
        return {task_id: "running" for task_id in self.running_tasks.keys()}

    async def get_available_tools(self) -> list:
        """获取可用工具列表（静态返回）"""
        # 从全局 Manus agent 中获取工具信息
        try:
            from .background import get_global_manus_instance
            agent = get_global_manus_instance()

            if agent is None:
                # 后备方案：如果全局实例不可用，创建临时实例
                logger.warning("全局 Manus 实例不可用，创建临时实例获取工具列表")
                agent = await Manus.create()
                should_cleanup = True
            else:
                should_cleanup = False

            tools = []

            # 获取工具集合中的工具
            for tool in agent.available_tools.tools:
                tools.append(
                    {
                        "name": tool.name,
                        "description": tool.description,
                        "category": "core",
                    }
                )

            # 添加 MCP 工具
            for tool in agent.mcp_clients.tools:
                tools.append(
                    {
                        "name": tool.name,
                        "description": tool.description,
                        "category": "mcp",
                        "server_id": getattr(tool, "server_id", "unknown"),
                    }
                )

            # 只有临时创建的实例才需要清理
            if should_cleanup:
                await agent.cleanup()

            return tools

        except Exception as e:
            logger.error(f"获取工具列表失败: {str(e)}")
            # 返回默认工具列表
            return [
                {
                    "name": "python_execute",
                    "description": "执行Python代码",
                    "category": "core",
                },
                {
                    "name": "str_replace_editor",
                    "description": "文件编辑工具",
                    "category": "core",
                },
                {"name": "ask_human", "description": "询问用户", "category": "core"},
                {"name": "terminate", "description": "终止任务", "category": "core"},
            ]
