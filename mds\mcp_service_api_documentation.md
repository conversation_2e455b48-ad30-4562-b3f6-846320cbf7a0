# MCP 服务管理 API 接口文档

## 概述

MCP (Model Context Protocol) 服务管理 API 提供了对 MCP 服务器的完整生命周期管理，包括服务器配置的创建、查询、更新、删除，以及连接管理和状态监控。

## 基础信息

- **API 前缀**: `/api/mcp`
- **标签**: MCP管理
- **认证**: 无需认证（内部服务）

## 数据模型

### 枚举类型

#### MCPConnectionType (连接类型)
- `stdio`: 标准输入输出连接
- `sse`: Server-Sent Events 连接

#### MCPServerStatus (服务器状态)
- `disconnected`: 已断开连接
- `connecting`: 连接中
- `connected`: 已连接
- `error`: 错误状态

### 核心数据模型

#### MCPServerInfo (服务器信息)
```json
{
  "server_id": "string",           // 服务器ID
  "name": "string",                // 服务器名称
  "type": "stdio|sse",             // 连接类型
  "description": "string",         // 服务器描述
  "enabled": true,                 // 是否启用
  "status": "disconnected",        // 连接状态
  "command": "string",             // stdio连接命令
  "args": ["string"],              // stdio连接参数
  "url": "string",                 // sse连接URL
  "tools": [                       // 可用工具列表
    {
      "name": "string",
      "description": "string",
      "parameters": {}
    }
  ],
  "error_message": "string",       // 错误信息
  "connected_at": "string"         // 连接时间
}
```

## API 接口

### 1. 获取MCP服务器列表

**GET** `/api/mcp/servers`

获取所有MCP服务器的列表和状态信息。

#### 响应
```json
{
  "servers": [MCPServerInfo],
  "total": 0
}
```

#### 示例
```bash
curl -X GET "http://localhost:8000/api/mcp/servers"
```

### 2. 获取指定MCP服务器信息

**GET** `/api/mcp/servers/{server_id}`

获取指定MCP服务器的详细信息。

#### 路径参数
- `server_id` (string): 服务器ID

#### 响应
- **200**: 返回 `MCPServerInfo`
- **404**: 服务器不存在

#### 示例
```bash
curl -X GET "http://localhost:8000/api/mcp/servers/my_server"
```

### 3. 创建新的MCP服务器配置

**POST** `/api/mcp/servers`

创建新的MCP服务器配置。

#### 请求体 (MCPServerConfigCreate)
```json
{
  "name": "string",                // 必填：服务器名称
  "type": "stdio|sse",             // 必填：连接类型
  "description": "string",         // 可选：服务器描述
  "enabled": true,                 // 可选：是否启用，默认true
  
  // stdio连接配置（type为stdio时必填）
  "command": "string",             // stdio连接的命令
  "args": ["string"],              // stdio连接的参数
  
  // sse连接配置（type为sse时必填）
  "url": "string"                  // sse连接的URL
}
```

#### 响应
- **200**: 返回创建的 `MCPServerInfo`
- **400**: 请求参数错误

#### 示例
```bash
# 创建stdio类型服务器
curl -X POST "http://localhost:8000/api/mcp/servers" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "My MCP Server",
    "type": "stdio",
    "description": "测试MCP服务器",
    "command": "python",
    "args": ["-m", "mcp_server"]
  }'

# 创建sse类型服务器
curl -X POST "http://localhost:8000/api/mcp/servers" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "SSE MCP Server",
    "type": "sse",
    "description": "SSE连接的MCP服务器",
    "url": "http://localhost:3000/sse"
  }'
```

### 4. 更新MCP服务器配置

**PUT** `/api/mcp/servers/{server_id}`

更新指定MCP服务器的配置。

#### 路径参数
- `server_id` (string): 服务器ID

#### 请求体 (MCPServerConfigUpdate)
```json
{
  "name": "string",                // 可选：服务器名称
  "description": "string",         // 可选：服务器描述
  "enabled": true,                 // 可选：是否启用
  "command": "string",             // 可选：stdio连接的命令
  "args": ["string"],              // 可选：stdio连接的参数
  "url": "string"                  // 可选：sse连接的URL
}
```

#### 响应
- **200**: 返回更新后的 `MCPServerInfo`
- **404**: 服务器不存在

#### 示例
```bash
curl -X PUT "http://localhost:8000/api/mcp/servers/my_server" \
  -H "Content-Type: application/json" \
  -d '{
    "description": "更新后的描述",
    "enabled": false
  }'
```

### 5. 删除MCP服务器配置

**DELETE** `/api/mcp/servers/{server_id}`

删除指定的MCP服务器配置。

#### 路径参数
- `server_id` (string): 服务器ID

#### 响应
```json
{
  "success": true,
  "message": "服务器 {server_id} 已成功删除"
}
```

#### 示例
```bash
curl -X DELETE "http://localhost:8000/api/mcp/servers/my_server"
```

### 6. 连接到MCP服务器

**POST** `/api/mcp/servers/{server_id}/connect`

连接到指定的MCP服务器。

#### 路径参数
- `server_id` (string): 服务器ID

#### 请求体 (MCPConnectionRequest)
```json
{
  "force": false                   // 可选：是否强制重连，默认false
}
```

#### 响应 (MCPConnectionResponse)
```json
{
  "success": true,
  "message": "成功连接到服务器 {server_id}",
  "server_info": MCPServerInfo     // 服务器信息
}
```

#### 示例
```bash
# 普通连接
curl -X POST "http://localhost:8000/api/mcp/servers/my_server/connect" \
  -H "Content-Type: application/json" \
  -d '{}'

# 强制重连
curl -X POST "http://localhost:8000/api/mcp/servers/my_server/connect" \
  -H "Content-Type: application/json" \
  -d '{"force": true}'
```

### 7. 断开MCP服务器连接

**POST** `/api/mcp/servers/{server_id}/disconnect`

断开与指定MCP服务器的连接。

#### 路径参数
- `server_id` (string): 服务器ID

#### 响应
```json
{
  "success": true,
  "message": "成功断开与服务器 {server_id} 的连接"
}
```

#### 示例
```bash
curl -X POST "http://localhost:8000/api/mcp/servers/my_server/disconnect"
```

### 8. 获取MCP服务器状态

**GET** `/api/mcp/servers/{server_id}/status`

获取指定MCP服务器的详细状态信息。

#### 路径参数
- `server_id` (string): 服务器ID

#### 响应 (MCPServerStatusResponse)
```json
{
  "server_id": "string",           // 服务器ID
  "status": "connected",           // 连接状态
  "tools_count": 5,                // 工具数量
  "error_message": null,           // 错误信息
  "uptime": 3600                   // 运行时间(秒)
}
```

#### 示例
```bash
curl -X GET "http://localhost:8000/api/mcp/servers/my_server/status"
```

## 错误处理

所有接口都遵循统一的错误响应格式：

```json
{
  "detail": "错误描述信息"
}
```

常见错误码：
- **400**: 请求参数错误
- **404**: 资源不存在
- **500**: 服务器内部错误

## 使用流程

### 典型的MCP服务器管理流程：

1. **创建服务器配置**: `POST /api/mcp/servers`
2. **查看服务器列表**: `GET /api/mcp/servers`
3. **连接服务器**: `POST /api/mcp/servers/{server_id}/connect`
4. **检查连接状态**: `GET /api/mcp/servers/{server_id}/status`
5. **使用服务器工具**: 通过其他API调用MCP工具
6. **断开连接**: `POST /api/mcp/servers/{server_id}/disconnect`
7. **更新配置**: `PUT /api/mcp/servers/{server_id}`
8. **删除配置**: `DELETE /api/mcp/servers/{server_id}`

## 注意事项

1. **配置持久化**: 所有配置更改都会保存到 `config/mcp.json` 文件中
2. **连接状态**: 服务器重启后需要重新连接MCP服务器
3. **工具可用性**: 只有在连接状态下才能使用MCP服务器提供的工具
4. **错误恢复**: 连接失败时会记录错误信息，可通过状态接口查看
5. **并发安全**: 支持多个客户端同时管理MCP服务器

## 配置文件格式

MCP配置文件 (`config/mcp.json`) 的格式：

```json
{
  "mcpServers": {
    "server_id": {
      "name": "服务器名称",
      "type": "stdio",
      "description": "服务器描述",
      "enabled": true,
      "command": "python",
      "args": ["-m", "mcp_server"]
    }
  }
}
```
