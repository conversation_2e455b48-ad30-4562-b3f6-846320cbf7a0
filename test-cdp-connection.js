/**
 * 测试CDP连接功能
 * 这个脚本用于验证智能CDP连接是否正常工作
 */

import { CDPManager } from './external/playwright-mcp/lib/cdpManager.js';

async function testCDPConnection() {
    console.log('🚀 开始测试CDP连接功能...\n');

    // 模拟浏览器配置
    const browserConfig = {
        browserName: 'chromium',
        launchOptions: {
            channel: 'chrome',
            headless: false,
            chromiumSandbox: false,
            args: [
                '--no-sandbox',
                '--disable-web-security',
                '--disable-features=VizDisplayCompositor'
            ]
        },
        contextOptions: {
            viewport: {
                width: 1280,
                height: 720
            }
        },
        isolated: false
    };

    const cdpManager = new CDPManager(browserConfig);

    try {
        // 测试1: 自动检测或启动浏览器
        console.log('📋 测试1: 自动检测或启动浏览器 (cdpEndpoint = "auto")');
        const autoEndpoint = await cdpManager.resolveCDPEndpoint('auto');
        console.log(`✅ 自动解析结果: ${autoEndpoint}\n`);

        // 验证连接是否可用
        await testEndpointConnection(autoEndpoint);

        // 测试2: 使用固定端口
        console.log('📋 测试2: 使用固定端口 (cdpEndpoint = "9222")');
        const fixedEndpoint = await cdpManager.resolveCDPEndpoint('9222');
        console.log(`✅ 固定端口解析结果: ${fixedEndpoint}\n`);

        // 测试3: 使用完整URL
        console.log('📋 测试3: 使用完整URL (cdpEndpoint = "http://localhost:9222")');
        const urlEndpoint = await cdpManager.resolveCDPEndpoint('http://localhost:9222');
        console.log(`✅ 完整URL解析结果: ${urlEndpoint}\n`);

        console.log('🎉 所有测试通过！CDP连接功能正常工作。');

    } catch (error) {
        console.error('❌ 测试失败:', error.message);
        console.error('详细错误:', error);
    }
}

async function testEndpointConnection(endpoint) {
    try {
        console.log(`🔍 验证端点连接: ${endpoint}`);
        
        const response = await fetch(`${endpoint}/json/version`, {
            signal: AbortSignal.timeout(5000)
        });

        if (response.ok) {
            const version = await response.json();
            console.log(`✅ 连接成功! 浏览器信息: ${version.product}`);
            console.log(`   用户代理: ${version.userAgent}`);
            console.log(`   协议版本: ${version.protocolVersion}\n`);
        } else {
            console.log(`⚠️  连接响应异常: ${response.status} ${response.statusText}\n`);
        }
    } catch (error) {
        console.log(`⚠️  连接测试失败: ${error.message}\n`);
    }
}

// 运行测试
testCDPConnection().catch(console.error);
