/**
 * 带调试的CDP连接测试
 */

// 启用调试日志
process.env.DEBUG = 'pw:mcp:cdp-manager';

import { CDPManager } from './external/playwright-mcp/lib/cdpManager.js';

async function testWithDebug() {
    console.log('🚀 开始调试测试...\n');

    const browserConfig = {
        browserName: 'chromium',
        launchOptions: {
            channel: 'chrome',
            headless: false,
            chromiumSandbox: false,
            args: ['--no-sandbox']
        },
        contextOptions: {
            viewport: { width: 1280, height: 720 }
        },
        isolated: false
    };

    const cdpManager = new CDPManager(browserConfig);

    try {
        console.log('📋 测试现有浏览器检测（带调试）...');
        const existingBrowser = await cdpManager._detectExistingBrowser();
        if (existingBrowser) {
            console.log(`✅ 检测到现有浏览器: ${existingBrowser}`);
            
            // 验证连接
            console.log('📋 验证连接...');
            const response = await fetch(`${existingBrowser}/json/version`);
            if (response.ok) {
                const version = await response.json();
                console.log('✅ 连接验证成功!');
                console.log('浏览器信息:', JSON.stringify(version, null, 2));
            }
        } else {
            console.log('ℹ️  未检测到现有浏览器实例');
        }

        console.log('\n🎉 调试测试完成！');

    } catch (error) {
        console.error('❌ 测试失败:', error.message);
        console.error('详细错误:', error);
    }
}

testWithDebug().catch(console.error);
