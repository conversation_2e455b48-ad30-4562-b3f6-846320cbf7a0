"""
Agent 路由模块
"""
from fastapi import APIRouter, HTTPException # type: ignore
from typing import List

from web_api.schemas.agent import AgentRunRequest, AgentInfo, AgentType
from web_api.schemas.task import TaskResponse
from web_api.services.agent_service import agent_service

# 创建路由器
router = APIRouter(
    prefix="/agents",
    tags=["agents"],
    responses={404: {"description": "Not found"}},
)


@router.get("/", response_model=List[AgentInfo])
async def get_agents():
    """
    获取所有支持的 Agent 类型
    """
    agents = []
    for agent_type in AgentType:
        agent_info = agent_service.get_agent_info(agent_type)
        if agent_info:
            agents.append(AgentInfo(**agent_info))
    return agents


@router.get("/{agent_type}", response_model=AgentInfo)
async def get_agent(agent_type: AgentType):
    """
    获取指定类型的 Agent 信息
    """
    agent_info = agent_service.get_agent_info(agent_type)
    if not agent_info:
        raise HTTPException(status_code=404, detail=f"Agent 类型 {agent_type} 不存在")
    return AgentInfo(**agent_info)


@router.post("/run", response_model=TaskResponse)
async def run_agent(request: AgentRunRequest):
    """
    运行 Agent 任务
    """
    try:
        task_info = agent_service.run_agent_task(
            request.agent_type,
            request.instruction,
            request.parameters
        )
        return task_info
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"运行 Agent 任务失败: {str(e)}")
