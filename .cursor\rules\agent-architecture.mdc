# Agent Architecture Guide

## Agent Hierarchy

- **Base Agent**: [app/agent/base.py](mdc:app/agent/base.py) - Abstract base class for all agents
- **ToolCall Agent**: [app/agent/toolcall.py](mdc:app/agent/toolcall.py) - Agent with tool calling capabilities
- **Manus Agent**: [app/agent/manus.py](mdc:app/agent/manus.py) - Main versatile agent implementation

## Agent Types

1. **Manus** - General-purpose agent with comprehensive tool access
2. **Browser Agent** - Specialized for web automation tasks
3. **SWE Agent** - Software engineering focused agent
4. **Data Analysis Agent** - Specialized for data analysis tasks
5. **MCP Agent** - Agent with Model Context Protocol integration
6. **React Agent** - ReAct (Reasoning and Acting) pattern implementation

## Agent Implementation Patterns

### Async/Await Pattern

All agents use async/await for non-blocking operations:

```python
async def run(self, prompt: str) -> None:
    # Agent execution logic
```

### Tool Integration

Agents use `ToolCollection` to manage available tools:

```python
available_tools: ToolCollection = Field(default_factory=lambda: ToolCollection(...))
```

### State Management

- Use Pydantic fields for agent state
- Implement proper cleanup in `cleanup()` method
- Track execution steps and observations

### Error Handling

- Use structured exceptions from [app/exceptions.py](mdc:app/exceptions.py)
- Implement graceful degradation for tool failures
- Log errors using the configured logger

## Agent Lifecycle

1. **Creation**: `await Agent.create()` - Async factory method
2. **Execution**: `await agent.run(prompt)` - Main execution
3. **Cleanup**: `await agent.cleanup()` - Resource cleanup

## Best Practices

- Inherit from appropriate base class
- Use type hints and Pydantic validation
- Implement proper error handling and logging
- Follow the established async patterns
- Use the tool collection system for extensibility
  description:
  globs:
  alwaysApply: false

---
