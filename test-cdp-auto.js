/**
 * 测试自动模式CDP连接
 */

// 启用调试日志
process.env.DEBUG = 'pw:mcp:cdp-manager';

import { CDPManager } from './external/playwright-mcp/lib/cdpManager.js';

async function testAutoMode() {
    console.log('🚀 开始自动模式测试...\n');

    const browserConfig = {
        browserName: 'chromium',
        launchOptions: {
            channel: 'chrome',
            headless: false,
            chromiumSandbox: false,
            args: ['--no-sandbox']
        },
        contextOptions: {
            viewport: { width: 1280, height: 720 }
        },
        isolated: false
    };

    const cdpManager = new CDPManager(browserConfig);

    try {
        console.log('📋 测试自动模式 (cdpEndpoint = "auto")...');
        const autoEndpoint = await cdpManager.resolveCDPEndpoint('auto');
        console.log(`✅ 自动解析结果: ${autoEndpoint}`);

        // 验证连接
        console.log('📋 验证自动解析的端点...');
        const response = await fetch(`${autoEndpoint}/json/version`);
        if (response.ok) {
            const version = await response.json();
            console.log('✅ 自动端点连接验证成功!');
            console.log(`浏览器: ${version.Browser || version.product}`);
            console.log(`协议版本: ${version['Protocol-Version']}`);
        }

        console.log('\n🎉 自动模式测试完成！');

    } catch (error) {
        console.error('❌ 测试失败:', error.message);
        console.error('详细错误:', error);
    }
}

testAutoMode().catch(console.error);
