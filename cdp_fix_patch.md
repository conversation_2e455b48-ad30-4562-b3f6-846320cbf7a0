# Playwright-MCP CDP修复补丁

## 修复说明
修复了playwright-mcp的CDP模式连接问题，解决了"auto"端点解析和连接超时问题。

## 修改文件

### 1. external/playwright-mcp/lib/browserContextFactory.js

#### 添加导入
在文件顶部添加：
```javascript
import { CDPManager } from './cdpManager.js';
```

#### 修改CdpContextFactory._doObtainBrowser方法
将原来的：
```javascript
async _doObtainBrowser() {
    return playwright.chromium.connectOverCDP(this.browserConfig.cdpEndpoint);
}
```

替换为：
```javascript
async _doObtainBrowser() {
    // 使用CDP管理器解析端点
    const cdpManager = new CDPManager(this.browserConfig);
    const resolvedEndpoint = await cdpManager.resolveCDPEndpoint(this.browserConfig.cdpEndpoint);
    testDebug(`CDP endpoint resolved: ${this.browserConfig.cdpEndpoint} -> ${resolvedEndpoint}`);
    return playwright.chromium.connectOverCDP(resolvedEndpoint);
}
```

### 2. external/playwright-mcp/lib/cdpManager.js

#### 增强健康检查功能
在 `_testBrowserHealth` 方法中添加实际的Playwright连接测试：

```javascript
/**
 * 测试Playwright连接
 * @param {string} endpoint - CDP端点
 * @returns {Promise<boolean>} 连接是否成功
 */
async _testPlaywrightConnection(endpoint) {
    try {
        const playwright = await import('playwright');
        
        debugLogger(`尝试Playwright连接: ${endpoint}`);
        
        // 设置较短的超时时间进行快速测试
        const browser = await playwright.chromium.connectOverCDP(endpoint, {
            timeout: 10000 // 10秒超时
        });
        
        debugLogger('Playwright连接成功，测试基本功能...');
        
        // 测试基本功能
        const contexts = browser.contexts();
        debugLogger(`找到 ${contexts.length} 个上下文`);
        
        // 关闭连接
        await browser.close();
        debugLogger('Playwright连接测试完成，连接正常');
        return true;
        
    } catch (error) {
        debugLogger(`Playwright连接测试失败: ${error.message}`);
        return false;
    }
}
```

#### 添加智能端口选择
添加 `_findAvailablePortExcluding` 方法避免端口冲突。

## 应用补丁的步骤

1. 备份当前的 `external/playwright-mcp/lib/` 目录
2. 重新编译/更新 playwright-mcp
3. 应用上述修改到新的文件中
4. 测试CDP功能是否正常

## 验证修复
运行以下命令验证修复：
```bash
conda activate open_manus
python run_with_playwright_mcp.py
```

然后输入测试命令：
```
新建标签页，打开bing，搜索重庆天气
```

应该能看到成功连接和操作，不会出现 "Invalid URL" 或超时错误。
