# 前端 Agent 交互 API 集成指南

## 📋 概述

本文档为前端开发者提供完整的 Agent 交互 API 集成指南。通过本 API，前端可以实现：

- 🤖 **实时 Agent 交互** - 创建会话、发送任务、监控执行
- 🧠 **思考过程可视化** - 实时显示 Agent 的思考和推理过程
- 🔧 **工具调用监控** - 详细展示每个工具的调用和结果
- 📊 **执行分析** - 提供丰富的执行统计和性能数据
- 💬 **实时通信** - WebSocket 双向通信，零延迟体验

## 🌐 服务信息

```javascript
const API_CONFIG = {
  baseURL: "http://127.0.0.1:8000",
  wsURL: "ws://127.0.0.1:8000",
  timeout: 30000,
  retryAttempts: 3,
};
```

## 🚀 快速开始

### 1. 基础 HTTP 客户端设置

```javascript
// 推荐使用 axios 或 fetch
import axios from "axios";

const apiClient = axios.create({
  baseURL: "http://127.0.0.1:8000",
  timeout: 30000,
  headers: {
    "Content-Type": "application/json",
  },
});

// 请求拦截器
apiClient.interceptors.request.use((config) => {
  console.log(`🚀 API Request: ${config.method?.toUpperCase()} ${config.url}`);
  return config;
});

// 响应拦截器
apiClient.interceptors.response.use(
  (response) => {
    console.log(`✅ API Response: ${response.status} ${response.config.url}`);
    return response;
  },
  (error) => {
    console.error(
      `❌ API Error: ${error.response?.status} ${error.config?.url}`,
      error.response?.data
    );
    return Promise.reject(error);
  }
);
```

### 2. WebSocket 客户端设置

```javascript
class AgentWebSocketClient {
  constructor(sessionId) {
    this.sessionId = sessionId;
    this.ws = null;
    this.reconnectAttempts = 0;
    this.maxReconnectAttempts = 5;
    this.messageHandlers = new Map();
    this.isConnected = false;
  }

  connect() {
    const wsUrl = `ws://127.0.0.1:8000/ws/sessions/${this.sessionId}`;
    this.ws = new WebSocket(wsUrl);

    this.ws.onopen = (event) => {
      console.log("🔗 WebSocket 连接已建立");
      this.isConnected = true;
      this.reconnectAttempts = 0;
      this.onConnectionOpen?.(event);
    };

    this.ws.onmessage = (event) => {
      try {
        const message = JSON.parse(event.data);
        this.handleMessage(message);
      } catch (error) {
        console.error("❌ WebSocket 消息解析失败:", error);
      }
    };

    this.ws.onclose = (event) => {
      console.log("🔌 WebSocket 连接已关闭");
      this.isConnected = false;
      this.onConnectionClose?.(event);
      this.attemptReconnect();
    };

    this.ws.onerror = (error) => {
      console.error("❌ WebSocket 错误:", error);
      this.onConnectionError?.(error);
    };
  }

  handleMessage(message) {
    const { type, data } = message;
    const handler = this.messageHandlers.get(type);
    if (handler) {
      handler(data, message);
    } else {
      console.log(`📨 未处理的消息类型: ${type}`, data);
    }
  }

  // 注册消息处理器
  on(messageType, handler) {
    this.messageHandlers.set(messageType, handler);
  }

  // 发送消息
  send(message) {
    if (this.isConnected && this.ws.readyState === WebSocket.OPEN) {
      this.ws.send(JSON.stringify(message));
    } else {
      console.warn("⚠️ WebSocket 未连接，消息发送失败");
    }
  }

  // 订阅任务事件
  subscribeToTask(taskId) {
    this.send({
      type: "subscribe_task",
      task_id: taskId,
    });
  }

  // 获取状态
  getStatus() {
    this.send({
      type: "get_status",
    });
  }

  // 心跳检测
  ping() {
    this.send({ type: "ping" });
  }

  // 重连机制
  attemptReconnect() {
    if (this.reconnectAttempts < this.maxReconnectAttempts) {
      this.reconnectAttempts++;
      console.log(
        `🔄 尝试重连 (${this.reconnectAttempts}/${this.maxReconnectAttempts})`
      );
      setTimeout(() => this.connect(), 2000 * this.reconnectAttempts);
    }
  }

  disconnect() {
    if (this.ws) {
      this.ws.close();
    }
  }
}
```

## 📡 核心 API 端点

### 1. 健康检查

```javascript
/**
 * 检查服务健康状态
 * @returns {Promise<Object>} 服务状态信息
 */
async function checkHealth() {
  try {
    const response = await apiClient.get("/api/health");
    return response.data;
    // 返回格式:
    // {
    //   status: "ok",
    //   message: "服务运行正常",
    //   version: "1.0.0",
    //   sessions: 3,
    //   tasks: 5,
    //   active_tasks: 2,
    //   websocket_connections: 1
    // }
  } catch (error) {
    throw new Error(`健康检查失败: ${error.message}`);
  }
}

// 使用示例
checkHealth()
  .then((status) => {
    console.log("✅ 服务状态:", status);
    if (status.status === "ok") {
      // 服务正常，可以继续操作
    }
  })
  .catch((error) => {
    console.error("❌ 服务异常:", error);
  });
```

### 2. 会话管理

```javascript
/**
 * 创建新会话
 * @returns {Promise<Object>} 会话信息
 */
async function createSession() {
  try {
    const response = await apiClient.post("/api/sessions");
    return response.data;
    // 返回格式:
    // {
    //   session_id: "uuid",
    //   status: "active",
    //   created_at: "2025-01-17T10:30:00.000Z",
    //   task_count: 0
    // }
  } catch (error) {
    throw new Error(`创建会话失败: ${error.message}`);
  }
}

/**
 * 获取会话信息
 * @param {string} sessionId - 会话ID
 * @returns {Promise<Object>} 会话详情
 */
async function getSession(sessionId) {
  try {
    const response = await apiClient.get(`/api/sessions/${sessionId}`);
    return response.data;
  } catch (error) {
    if (error.response?.status === 404) {
      throw new Error("会话不存在");
    }
    throw new Error(`获取会话失败: ${error.message}`);
  }
}

/**
 * 获取所有会话
 * @returns {Promise<Array>} 会话列表
 */
async function getAllSessions() {
  try {
    const response = await apiClient.get("/api/sessions");
    return response.data;
  } catch (error) {
    throw new Error(`获取会话列表失败: ${error.message}`);
  }
}

// 使用示例
async function initializeSession() {
  try {
    const session = await createSession();
    console.log("✅ 会话创建成功:", session.session_id);

    // 保存会话ID到本地存储
    localStorage.setItem("currentSessionId", session.session_id);

    return session;
  } catch (error) {
    console.error("❌ 会话创建失败:", error);
    throw error;
  }
}
```

### 3. 任务管理

```javascript
/**
 * 创建任务
 * @param {string} sessionId - 会话ID
 * @param {string} message - 任务描述
 * @returns {Promise<Object>} 任务信息
 */
async function createTask(sessionId, message) {
  try {
    const response = await apiClient.post(`/api/sessions/${sessionId}/tasks`, {
      message: message,
    });
    return response.data;
    // 返回格式:
    // {
    //   task_id: "uuid",
    //   session_id: "uuid",
    //   message: "任务描述",
    //   status: "queued",
    //   current_step: 0,
    //   max_steps: 20,
    //   created_at: "2025-01-17T10:30:00.000Z",
    //   start_time: null,
    //   end_time: null,
    //   result: null,
    //   error_message: null,
    //   progress_percentage: 0.0
    // }
  } catch (error) {
    throw new Error(`创建任务失败: ${error.message}`);
  }
}

/**
 * 获取任务状态
 * @param {string} sessionId - 会话ID
 * @param {string} taskId - 任务ID
 * @returns {Promise<Object>} 任务状态
 */
async function getTaskStatus(sessionId, taskId) {
  try {
    const response = await apiClient.get(
      `/api/sessions/${sessionId}/tasks/${taskId}`
    );
    return response.data;
  } catch (error) {
    if (error.response?.status === 404) {
      throw new Error("任务不存在");
    }
    throw new Error(`获取任务状态失败: ${error.message}`);
  }
}

/**
 * 获取会话的所有任务
 * @param {string} sessionId - 会话ID
 * @returns {Promise<Array>} 任务列表
 */
async function getSessionTasks(sessionId) {
  try {
    const response = await apiClient.get(`/api/sessions/${sessionId}/tasks`);
    return response.data;
  } catch (error) {
    throw new Error(`获取任务列表失败: ${error.message}`);
  }
}

/**
 * 取消任务
 * @param {string} sessionId - 会话ID
 * @param {string} taskId - 任务ID
 * @returns {Promise<Object>} 取消结果
 */
async function cancelTask(sessionId, taskId) {
  try {
    const response = await apiClient.post(
      `/api/sessions/${sessionId}/tasks/${taskId}/cancel`
    );
    return response.data;
  } catch (error) {
    throw new Error(`取消任务失败: ${error.message}`);
  }
}

// 使用示例
async function executeTask(sessionId, instruction) {
  try {
    // 1. 创建任务
    const task = await createTask(sessionId, instruction);
    console.log("✅ 任务创建成功:", task.task_id);

    // 2. 轮询检查任务状态（如果不使用WebSocket）
    const checkStatus = async () => {
      const status = await getTaskStatus(sessionId, task.task_id);
      console.log(
        `📊 任务状态: ${status.status} (${status.progress_percentage}%)`
      );

      if (status.status === "completed") {
        console.log("✅ 任务完成:", status.result);
        return status;
      } else if (status.status === "error") {
        throw new Error(`任务执行失败: ${status.error_message}`);
      } else if (status.status === "running") {
        // 继续轮询
        setTimeout(checkStatus, 2000);
      }
    };

    // 开始状态检查
    setTimeout(checkStatus, 1000);

    return task;
  } catch (error) {
    console.error("❌ 任务执行失败:", error);
    throw error;
  }
}
```

### 4. Agent 详细信息 API

```javascript
/**
 * 获取任务详细执行信息
 * @param {string} taskId - 任务ID
 * @returns {Promise<Object|null>} 执行详情
 */
async function getTaskExecutionDetail(taskId) {
  try {
    const response = await apiClient.get(
      `/api/agent-details/tasks/${taskId}/execution`
    );
    return response.data;
    // 返回格式:
    // {
    //   task_id: "uuid",
    //   agent_type: "manus",
    //   instruction: "原始指令",
    //   status: "completed",
    //   current_step: 5,
    //   max_steps: 20,
    //   steps: [
    //     {
    //       step: 1,
    //       timestamp: "2025-01-17T10:30:00.000Z",
    //       action: "分析需求",
    //       status: "completed",
    //       thinking: { content: "...", reasoning: "..." },
    //       tool_calls: [...],
    //       duration: 5.67,
    //       observations: ["需求分析完成"]
    //     }
    //   ],
    //   start_time: "2025-01-17T10:30:00.000Z",
    //   end_time: "2025-01-17T10:32:30.000Z",
    //   total_duration: 150.5,
    //   final_result: "任务结果"
    // }
  } catch (error) {
    throw new Error(`获取执行详情失败: ${error.message}`);
  }
}

/**
 * 获取任务步骤汇总
 * @param {string} taskId - 任务ID
 * @returns {Promise<Object>} 步骤汇总
 */
async function getTaskSteps(taskId) {
  try {
    const response = await apiClient.get(
      `/api/agent-details/tasks/${taskId}/steps`
    );
    return response.data;
    // 返回格式:
    // {
    //   task_id: "uuid",
    //   total_steps: 5,
    //   steps: [
    //     {
    //       step: 1,
    //       action: "分析需求",
    //       status: "completed",
    //       timestamp: "2025-01-17T10:30:00.000Z",
    //       duration: 5.67,
    //       tool_calls_count: 1,
    //       observations_count: 1,
    //       has_error: false
    //     }
    //   ]
    // }
  } catch (error) {
    throw new Error(`获取步骤汇总失败: ${error.message}`);
  }
}

/**
 * 获取工具调用统计
 * @param {string} taskId - 任务ID
 * @returns {Promise<Object>} 工具调用统计
 */
async function getTaskToolCalls(taskId) {
  try {
    const response = await apiClient.get(
      `/api/agent-details/tasks/${taskId}/tools`
    );
    return response.data;
    // 返回格式:
    // {
    //   task_id: "uuid",
    //   total_tool_calls: 8,
    //   tool_calls: [...],
    //   tool_statistics: {
    //     "str_replace_editor": {
    //       count: 3,
    //       success_count: 3,
    //       error_count: 0,
    //       total_time: 3.45
    //     }
    //   }
    // }
  } catch (error) {
    throw new Error(`获取工具调用统计失败: ${error.message}`);
  }
}

/**
 * 获取会话执行汇总
 * @param {string} sessionId - 会话ID
 * @returns {Promise<Object>} 会话汇总
 */
async function getSessionSummary(sessionId) {
  try {
    const response = await apiClient.get(
      `/api/agent-details/sessions/${sessionId}/summary`
    );
    return response.data;
    // 返回格式:
    // {
    //   session_id: "uuid",
    //   total_tasks: 3,
    //   completed_tasks: 2,
    //   failed_tasks: 0,
    //   running_tasks: 1,
    //   cancelled_tasks: 0,
    //   total_execution_time: 245.6,
    //   tasks_summary: [...]
    // }
  } catch (error) {
    throw new Error(`获取会话汇总失败: ${error.message}`);
  }
}
```

## 🎯 完整集成示例

### React 组件示例

```jsx
import React, { useState, useEffect, useRef } from "react";

const AgentChat = () => {
  const [sessionId, setSessionId] = useState(null);
  const [currentTask, setCurrentTask] = useState(null);
  const [messages, setMessages] = useState([]);
  const [inputValue, setInputValue] = useState("");
  const [isConnected, setIsConnected] = useState(false);
  const [taskStatus, setTaskStatus] = useState(null);

  const wsClient = useRef(null);

  // 初始化会话和WebSocket连接
  useEffect(() => {
    initializeChat();
    return () => {
      if (wsClient.current) {
        wsClient.current.disconnect();
      }
    };
  }, []);

  const initializeChat = async () => {
    try {
      // 1. 检查服务健康状态
      const health = await checkHealth();
      if (health.status !== "ok") {
        throw new Error("服务不可用");
      }

      // 2. 创建会话
      const session = await createSession();
      setSessionId(session.session_id);

      // 3. 建立WebSocket连接
      wsClient.current = new AgentWebSocketClient(session.session_id);

      // 注册消息处理器
      wsClient.current.on("connection", (data) => {
        setIsConnected(true);
        addMessage("system", "连接已建立，可以开始对话");
      });

      wsClient.current.on("thinking", (data) => {
        addMessage("thinking", data.content, {
          reasoning: data.reasoning,
          decision: data.decision,
          step: data.step,
        });
      });

      wsClient.current.on("tool_call", (data) => {
        addMessage("tool_call", `调用工具: ${data.tool_name}`, {
          status: data.status,
          arguments: data.arguments,
          result: data.result,
          execution_time: data.execution_time,
        });
      });

      wsClient.current.on("step_update", (data) => {
        addMessage("step_update", `步骤 ${data.step}: ${data.action}`, {
          status: data.status,
          duration: data.duration,
          observations: data.observations,
        });
      });

      wsClient.current.on("task_status", (data) => {
        setTaskStatus(data);
        if (data.status === "completed") {
          addMessage("result", data.result || "任务完成");
        } else if (data.status === "error") {
          addMessage("error", data.error || "任务执行失败");
        }
      });

      wsClient.current.on("log", (data) => {
        console.log(`[${data.level}] ${data.component}: ${data.message}`);
      });

      // 连接WebSocket
      wsClient.current.connect();
    } catch (error) {
      console.error("初始化失败:", error);
      addMessage("error", `初始化失败: ${error.message}`);
    }
  };

  const addMessage = (type, content, extra = {}) => {
    const message = {
      id: Date.now() + Math.random(),
      type,
      content,
      timestamp: new Date(),
      ...extra,
    };
    setMessages((prev) => [...prev, message]);
  };

  const sendMessage = async () => {
    if (!inputValue.trim() || !sessionId) return;

    const userMessage = inputValue.trim();
    setInputValue("");

    // 添加用户消息
    addMessage("user", userMessage);

    try {
      // 创建任务
      const task = await createTask(sessionId, userMessage);
      setCurrentTask(task);

      // 订阅任务事件
      if (wsClient.current) {
        wsClient.current.subscribeToTask(task.task_id);
      }

      addMessage("system", `任务已创建: ${task.task_id}`);
    } catch (error) {
      console.error("发送消息失败:", error);
      addMessage("error", `发送失败: ${error.message}`);
    }
  };

  const cancelCurrentTask = async () => {
    if (!currentTask || !sessionId) return;

    try {
      await cancelTask(sessionId, currentTask.task_id);
      addMessage("system", "任务已取消");
      setCurrentTask(null);
      setTaskStatus(null);
    } catch (error) {
      console.error("取消任务失败:", error);
      addMessage("error", `取消失败: ${error.message}`);
    }
  };

  const renderMessage = (message) => {
    const { type, content, timestamp } = message;

    switch (type) {
      case "user":
        return (
          <div className="message user-message">
            <div className="message-content">{content}</div>
            <div className="message-time">{timestamp.toLocaleTimeString()}</div>
          </div>
        );

      case "thinking":
        return (
          <div className="message thinking-message">
            <div className="message-header">🤔 Agent 思考中...</div>
            <div className="message-content">{content}</div>
            {message.reasoning && (
              <div className="message-detail">推理: {message.reasoning}</div>
            )}
            <div className="message-time">
              步骤 {message.step} - {timestamp.toLocaleTimeString()}
            </div>
          </div>
        );

      case "tool_call":
        return (
          <div className="message tool-message">
            <div className="message-header">🔧 {content}</div>
            <div className="message-detail">
              状态: {message.status}
              {message.execution_time &&
                ` | 耗时: ${message.execution_time.toFixed(2)}s`}
            </div>
            <div className="message-time">{timestamp.toLocaleTimeString()}</div>
          </div>
        );

      case "step_update":
        return (
          <div className="message step-message">
            <div className="message-content">{content}</div>
            <div className="message-detail">
              状态: {message.status}
              {message.duration && ` | 耗时: ${message.duration.toFixed(2)}s`}
            </div>
            <div className="message-time">{timestamp.toLocaleTimeString()}</div>
          </div>
        );

      case "result":
        return (
          <div className="message result-message">
            <div className="message-header">✅ 任务完成</div>
            <div className="message-content">{content}</div>
            <div className="message-time">{timestamp.toLocaleTimeString()}</div>
          </div>
        );

      case "error":
        return (
          <div className="message error-message">
            <div className="message-header">❌ 错误</div>
            <div className="message-content">{content}</div>
            <div className="message-time">{timestamp.toLocaleTimeString()}</div>
          </div>
        );

      case "system":
        return (
          <div className="message system-message">
            <div className="message-content">{content}</div>
            <div className="message-time">{timestamp.toLocaleTimeString()}</div>
          </div>
        );

      default:
        return (
          <div className="message">
            <div className="message-content">{content}</div>
            <div className="message-time">{timestamp.toLocaleTimeString()}</div>
          </div>
        );
    }
  };

  return (
    <div className="agent-chat">
      {/* 连接状态 */}
      <div className="connection-status">
        <span
          className={`status-indicator ${
            isConnected ? "connected" : "disconnected"
          }`}
        >
          {isConnected ? "🟢 已连接" : "🔴 未连接"}
        </span>
        {sessionId && (
          <span className="session-id">会话: {sessionId.slice(0, 8)}...</span>
        )}
      </div>

      {/* 任务状态 */}
      {taskStatus && (
        <div className="task-status">
          <div className="status-bar">
            <span>状态: {taskStatus.status}</span>
            <span>进度: {taskStatus.progress_percentage?.toFixed(1)}%</span>
            <span>
              步骤: {taskStatus.current_step}/{taskStatus.max_steps}
            </span>
          </div>
          <div className="progress-bar">
            <div
              className="progress-fill"
              style={{ width: `${taskStatus.progress_percentage || 0}%` }}
            />
          </div>
        </div>
      )}

      {/* 消息列表 */}
      <div className="messages-container">
        {messages.map((message) => (
          <div key={message.id}>{renderMessage(message)}</div>
        ))}
      </div>

      {/* 输入区域 */}
      <div className="input-area">
        <input
          type="text"
          value={inputValue}
          onChange={(e) => setInputValue(e.target.value)}
          onKeyPress={(e) => e.key === "Enter" && sendMessage()}
          placeholder="输入您的指令..."
          disabled={!isConnected}
        />
        <button
          onClick={sendMessage}
          disabled={!isConnected || !inputValue.trim()}
        >
          发送
        </button>
        {currentTask && taskStatus?.status === "running" && (
          <button onClick={cancelCurrentTask} className="cancel-button">
            取消
          </button>
        )}
      </div>
    </div>
  );
};

export default AgentChat;
```

### Vue.js 组件示例

```vue
<template>
  <div class="agent-chat">
    <!-- 连接状态 -->
    <div class="connection-status">
      <span
        :class="[
          'status-indicator',
          isConnected ? 'connected' : 'disconnected',
        ]"
      >
        {{ isConnected ? "🟢 已连接" : "🔴 未连接" }}
      </span>
      <span v-if="sessionId" class="session-id">
        会话: {{ sessionId.slice(0, 8) }}...
      </span>
    </div>

    <!-- 任务状态 -->
    <div v-if="taskStatus" class="task-status">
      <div class="status-bar">
        <span>状态: {{ taskStatus.status }}</span>
        <span
          >进度: {{ (taskStatus.progress_percentage || 0).toFixed(1) }}%</span
        >
        <span
          >步骤: {{ taskStatus.current_step }}/{{ taskStatus.max_steps }}</span
        >
      </div>
      <div class="progress-bar">
        <div
          class="progress-fill"
          :style="{ width: `${taskStatus.progress_percentage || 0}%` }"
        />
      </div>
    </div>

    <!-- 消息列表 -->
    <div class="messages-container" ref="messagesContainer">
      <div v-for="message in messages" :key="message.id">
        <MessageComponent :message="message" />
      </div>
    </div>

    <!-- 输入区域 -->
    <div class="input-area">
      <input
        v-model="inputValue"
        @keyup.enter="sendMessage"
        placeholder="输入您的指令..."
        :disabled="!isConnected"
      />
      <button
        @click="sendMessage"
        :disabled="!isConnected || !inputValue.trim()"
      >
        发送
      </button>
      <button
        v-if="currentTask && taskStatus?.status === 'running'"
        @click="cancelCurrentTask"
        class="cancel-button"
      >
        取消
      </button>
    </div>
  </div>
</template>

<script>
import { ref, onMounted, onUnmounted, nextTick } from "vue";
import MessageComponent from "./MessageComponent.vue";

export default {
  name: "AgentChat",
  components: {
    MessageComponent,
  },
  setup() {
    const sessionId = ref(null);
    const currentTask = ref(null);
    const messages = ref([]);
    const inputValue = ref("");
    const isConnected = ref(false);
    const taskStatus = ref(null);
    const messagesContainer = ref(null);

    let wsClient = null;

    const addMessage = (type, content, extra = {}) => {
      const message = {
        id: Date.now() + Math.random(),
        type,
        content,
        timestamp: new Date(),
        ...extra,
      };
      messages.value.push(message);

      // 自动滚动到底部
      nextTick(() => {
        if (messagesContainer.value) {
          messagesContainer.value.scrollTop =
            messagesContainer.value.scrollHeight;
        }
      });
    };

    const initializeChat = async () => {
      try {
        // 检查服务健康状态
        const health = await checkHealth();
        if (health.status !== "ok") {
          throw new Error("服务不可用");
        }

        // 创建会话
        const session = await createSession();
        sessionId.value = session.session_id;

        // 建立WebSocket连接
        wsClient = new AgentWebSocketClient(session.session_id);

        // 注册消息处理器
        wsClient.on("connection", (data) => {
          isConnected.value = true;
          addMessage("system", "连接已建立，可以开始对话");
        });

        wsClient.on("thinking", (data) => {
          addMessage("thinking", data.content, {
            reasoning: data.reasoning,
            decision: data.decision,
            step: data.step,
          });
        });

        wsClient.on("tool_call", (data) => {
          addMessage("tool_call", `调用工具: ${data.tool_name}`, {
            status: data.status,
            arguments: data.arguments,
            result: data.result,
            execution_time: data.execution_time,
          });
        });

        wsClient.on("step_update", (data) => {
          addMessage("step_update", `步骤 ${data.step}: ${data.action}`, {
            status: data.status,
            duration: data.duration,
            observations: data.observations,
          });
        });

        wsClient.on("task_status", (data) => {
          taskStatus.value = data;
          if (data.status === "completed") {
            addMessage("result", data.result || "任务完成");
          } else if (data.status === "error") {
            addMessage("error", data.error || "任务执行失败");
          }
        });

        // 连接WebSocket
        wsClient.connect();
      } catch (error) {
        console.error("初始化失败:", error);
        addMessage("error", `初始化失败: ${error.message}`);
      }
    };

    const sendMessage = async () => {
      if (!inputValue.value.trim() || !sessionId.value) return;

      const userMessage = inputValue.value.trim();
      inputValue.value = "";

      addMessage("user", userMessage);

      try {
        const task = await createTask(sessionId.value, userMessage);
        currentTask.value = task;

        if (wsClient) {
          wsClient.subscribeToTask(task.task_id);
        }

        addMessage("system", `任务已创建: ${task.task_id}`);
      } catch (error) {
        console.error("发送消息失败:", error);
        addMessage("error", `发送失败: ${error.message}`);
      }
    };

    const cancelCurrentTask = async () => {
      if (!currentTask.value || !sessionId.value) return;

      try {
        await cancelTask(sessionId.value, currentTask.value.task_id);
        addMessage("system", "任务已取消");
        currentTask.value = null;
        taskStatus.value = null;
      } catch (error) {
        console.error("取消任务失败:", error);
        addMessage("error", `取消失败: ${error.message}`);
      }
    };

    onMounted(() => {
      initializeChat();
    });

    onUnmounted(() => {
      if (wsClient) {
        wsClient.disconnect();
      }
    });

    return {
      sessionId,
      currentTask,
      messages,
      inputValue,
      isConnected,
      taskStatus,
      messagesContainer,
      sendMessage,
      cancelCurrentTask,
    };
  },
};
</script>
```

## 🎨 CSS 样式参考

```css
.agent-chat {
  display: flex;
  flex-direction: column;
  height: 100vh;
  max-width: 800px;
  margin: 0 auto;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  overflow: hidden;
}

.connection-status {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 15px;
  background: #f5f5f5;
  border-bottom: 1px solid #e0e0e0;
  font-size: 14px;
}

.status-indicator.connected {
  color: #4caf50;
}

.status-indicator.disconnected {
  color: #f44336;
}

.session-id {
  color: #666;
  font-family: monospace;
}

.task-status {
  padding: 10px 15px;
  background: #fff3cd;
  border-bottom: 1px solid #e0e0e0;
}

.status-bar {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
  font-size: 14px;
}

.progress-bar {
  height: 4px;
  background: #e0e0e0;
  border-radius: 2px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: #2196f3;
  transition: width 0.3s ease;
}

.messages-container {
  flex: 1;
  overflow-y: auto;
  padding: 15px;
  background: #fafafa;
}

.message {
  margin-bottom: 15px;
  padding: 12px;
  border-radius: 8px;
  max-width: 80%;
}

.user-message {
  background: #2196f3;
  color: white;
  margin-left: auto;
}

.thinking-message {
  background: #fff3e0;
  border-left: 4px solid #ff9800;
}

.tool-message {
  background: #e8f5e8;
  border-left: 4px solid #4caf50;
}

.step-message {
  background: #e3f2fd;
  border-left: 4px solid #2196f3;
}

.result-message {
  background: #e8f5e8;
  border-left: 4px solid #4caf50;
}

.error-message {
  background: #ffebee;
  border-left: 4px solid #f44336;
}

.system-message {
  background: #f5f5f5;
  color: #666;
  font-style: italic;
  text-align: center;
}

.message-header {
  font-weight: bold;
  margin-bottom: 5px;
}

.message-content {
  line-height: 1.5;
  white-space: pre-wrap;
}

.message-detail {
  font-size: 12px;
  color: #666;
  margin-top: 5px;
}

.message-time {
  font-size: 11px;
  color: #999;
  margin-top: 5px;
  text-align: right;
}

.input-area {
  display: flex;
  padding: 15px;
  background: white;
  border-top: 1px solid #e0e0e0;
  gap: 10px;
}

.input-area input {
  flex: 1;
  padding: 10px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
}

.input-area input:focus {
  outline: none;
  border-color: #2196f3;
}

.input-area button {
  padding: 10px 20px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
}

.input-area button:not(.cancel-button) {
  background: #2196f3;
  color: white;
}

.input-area button:not(.cancel-button):hover {
  background: #1976d2;
}

.input-area button:disabled {
  background: #ccc;
  cursor: not-allowed;
}

.cancel-button {
  background: #f44336;
  color: white;
}

.cancel-button:hover {
  background: #d32f2f;
}
```

## 🔧 工具函数库

```javascript
// utils/agentApi.js
export class AgentApiClient {
  constructor(baseURL = "http://127.0.0.1:8000") {
    this.baseURL = baseURL;
    this.timeout = 30000;
  }

  async request(endpoint, options = {}) {
    const url = `${this.baseURL}${endpoint}`;
    const config = {
      timeout: this.timeout,
      headers: {
        "Content-Type": "application/json",
        ...options.headers,
      },
      ...options,
    };

    try {
      const response = await fetch(url, config);

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(
          errorData.detail || `HTTP ${response.status}: ${response.statusText}`
        );
      }

      return await response.json();
    } catch (error) {
      if (error.name === "AbortError") {
        throw new Error("请求超时");
      }
      throw error;
    }
  }

  // 健康检查
  async checkHealth() {
    return this.request("/api/health");
  }

  // 会话管理
  async createSession() {
    return this.request("/api/sessions", { method: "POST" });
  }

  async getSession(sessionId) {
    return this.request(`/api/sessions/${sessionId}`);
  }

  async getAllSessions() {
    return this.request("/api/sessions");
  }

  // 任务管理
  async createTask(sessionId, message) {
    return this.request(`/api/sessions/${sessionId}/tasks`, {
      method: "POST",
      body: JSON.stringify({ message }),
    });
  }

  async getTaskStatus(sessionId, taskId) {
    return this.request(`/api/sessions/${sessionId}/tasks/${taskId}`);
  }

  async getSessionTasks(sessionId) {
    return this.request(`/api/sessions/${sessionId}/tasks`);
  }

  async cancelTask(sessionId, taskId) {
    return this.request(`/api/sessions/${sessionId}/tasks/${taskId}/cancel`, {
      method: "POST",
    });
  }

  // Agent 详细信息
  async getTaskExecutionDetail(taskId) {
    return this.request(`/api/agent-details/tasks/${taskId}/execution`);
  }

  async getTaskSteps(taskId) {
    return this.request(`/api/agent-details/tasks/${taskId}/steps`);
  }

  async getTaskToolCalls(taskId) {
    return this.request(`/api/agent-details/tasks/${taskId}/tools`);
  }

  async getSessionSummary(sessionId) {
    return this.request(`/api/agent-details/sessions/${sessionId}/summary`);
  }
}

// 创建全局实例
export const agentApi = new AgentApiClient();

// 导出便捷函数
export const {
  checkHealth,
  createSession,
  getSession,
  getAllSessions,
  createTask,
  getTaskStatus,
  getSessionTasks,
  cancelTask,
  getTaskExecutionDetail,
  getTaskSteps,
  getTaskToolCalls,
  getSessionSummary,
} = agentApi;
```

## 📊 数据可视化组件

```jsx
// components/TaskAnalytics.jsx
import React, { useState, useEffect } from "react";
import { getTaskExecutionDetail, getTaskToolCalls } from "../utils/agentApi";

const TaskAnalytics = ({ taskId }) => {
  const [executionDetail, setExecutionDetail] = useState(null);
  const [toolStats, setToolStats] = useState(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    if (taskId) {
      loadTaskAnalytics();
    }
  }, [taskId]);

  const loadTaskAnalytics = async () => {
    try {
      setLoading(true);
      const [detail, tools] = await Promise.all([
        getTaskExecutionDetail(taskId),
        getTaskToolCalls(taskId),
      ]);

      setExecutionDetail(detail);
      setToolStats(tools);
    } catch (error) {
      console.error("加载任务分析失败:", error);
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return <div className="loading">加载中...</div>;
  }

  if (!executionDetail) {
    return <div className="no-data">暂无数据</div>;
  }

  return (
    <div className="task-analytics">
      {/* 执行概览 */}
      <div className="analytics-section">
        <h3>📊 执行概览</h3>
        <div className="stats-grid">
          <div className="stat-item">
            <div className="stat-value">
              {executionDetail.steps?.length || 0}
            </div>
            <div className="stat-label">总步骤</div>
          </div>
          <div className="stat-item">
            <div className="stat-value">
              {executionDetail.total_duration?.toFixed(2) || 0}s
            </div>
            <div className="stat-label">总耗时</div>
          </div>
          <div className="stat-item">
            <div className="stat-value">{toolStats?.total_tool_calls || 0}</div>
            <div className="stat-label">工具调用</div>
          </div>
          <div className="stat-item">
            <div className="stat-value">{executionDetail.status}</div>
            <div className="stat-label">状态</div>
          </div>
        </div>
      </div>

      {/* 步骤时间线 */}
      <div className="analytics-section">
        <h3>⏱️ 执行时间线</h3>
        <div className="timeline">
          {executionDetail.steps?.map((step, index) => (
            <div key={index} className="timeline-item">
              <div className="timeline-marker" />
              <div className="timeline-content">
                <div className="step-header">
                  <span className="step-number">步骤 {step.step}</span>
                  <span className="step-status">{step.status}</span>
                  <span className="step-duration">
                    {step.duration?.toFixed(2)}s
                  </span>
                </div>
                <div className="step-action">{step.action}</div>
                {step.thinking && (
                  <div className="step-thinking">
                    💭 {step.thinking.content?.slice(0, 100)}...
                  </div>
                )}
                {step.tool_calls?.length > 0 && (
                  <div className="step-tools">
                    🔧 工具:{" "}
                    {step.tool_calls.map((t) => t.tool_name).join(", ")}
                  </div>
                )}
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* 工具使用统计 */}
      {toolStats?.tool_statistics && (
        <div className="analytics-section">
          <h3>🔧 工具使用统计</h3>
          <div className="tool-stats">
            {Object.entries(toolStats.tool_statistics).map(
              ([toolName, stats]) => (
                <div key={toolName} className="tool-stat-item">
                  <div className="tool-name">{toolName}</div>
                  <div className="tool-metrics">
                    <span>调用: {stats.count}次</span>
                    <span>
                      成功率:{" "}
                      {((stats.success_count / stats.count) * 100).toFixed(1)}%
                    </span>
                    <span>总耗时: {stats.total_time?.toFixed(2)}s</span>
                  </div>
                  <div className="tool-progress">
                    <div
                      className="tool-progress-bar"
                      style={{
                        width: `${(stats.success_count / stats.count) * 100}%`,
                      }}
                    />
                  </div>
                </div>
              )
            )}
          </div>
        </div>
      )}
    </div>
  );
};

export default TaskAnalytics;
```

## 🚨 错误处理最佳实践

### 1. API 错误处理

```javascript
// utils/errorHandler.js
export class ApiError extends Error {
  constructor(message, status, code) {
    super(message);
    this.name = "ApiError";
    this.status = status;
    this.code = code;
  }
}

export const handleApiError = (error) => {
  if (error.response) {
    // 服务器响应错误
    const { status, data } = error.response;
    switch (status) {
      case 400:
        return new ApiError("请求参数错误", status, "BAD_REQUEST");
      case 401:
        return new ApiError("未授权访问", status, "UNAUTHORIZED");
      case 404:
        return new ApiError("资源不存在", status, "NOT_FOUND");
      case 500:
        return new ApiError("服务器内部错误", status, "INTERNAL_ERROR");
      default:
        return new ApiError(data?.detail || "未知错误", status, "UNKNOWN");
    }
  } else if (error.request) {
    // 网络错误
    return new ApiError("网络连接失败", 0, "NETWORK_ERROR");
  } else {
    // 其他错误
    return new ApiError(error.message, 0, "CLIENT_ERROR");
  }
};

// 使用示例
async function safeApiCall(apiFunction, ...args) {
  try {
    return await apiFunction(...args);
  } catch (error) {
    const apiError = handleApiError(error);
    console.error(`API调用失败: ${apiError.message}`, apiError);
    throw apiError;
  }
}
```

### 2. WebSocket 错误处理

```javascript
// utils/websocketErrorHandler.js
export class WebSocketManager extends AgentWebSocketClient {
  constructor(sessionId, options = {}) {
    super(sessionId);
    this.options = {
      maxReconnectAttempts: 5,
      reconnectInterval: 2000,
      heartbeatInterval: 30000,
      ...options,
    };
    this.heartbeatTimer = null;
    this.connectionState = "disconnected";
    this.errorHandlers = new Map();
  }

  connect() {
    this.connectionState = "connecting";
    super.connect();

    // 启动心跳
    this.startHeartbeat();
  }

  onConnectionOpen(event) {
    this.connectionState = "connected";
    console.log("✅ WebSocket 连接成功");
    this.emit("connectionStateChange", "connected");
  }

  onConnectionClose(event) {
    this.connectionState = "disconnected";
    this.stopHeartbeat();

    if (event.code === 1000) {
      console.log("🔌 WebSocket 正常关闭");
    } else {
      console.warn(`⚠️ WebSocket 异常关闭: ${event.code} - ${event.reason}`);
      this.handleConnectionError("CONNECTION_CLOSED", event);
    }

    this.emit("connectionStateChange", "disconnected");
  }

  onConnectionError(error) {
    this.connectionState = "error";
    console.error("❌ WebSocket 连接错误:", error);
    this.handleConnectionError("CONNECTION_ERROR", error);
    this.emit("connectionStateChange", "error");
  }

  handleConnectionError(type, details) {
    const handler = this.errorHandlers.get(type);
    if (handler) {
      handler(details);
    }

    // 自动重连
    if (this.reconnectAttempts < this.options.maxReconnectAttempts) {
      this.scheduleReconnect();
    } else {
      console.error("❌ 达到最大重连次数，停止重连");
      this.emit("maxReconnectAttemptsReached");
    }
  }

  scheduleReconnect() {
    const delay =
      this.options.reconnectInterval * Math.pow(2, this.reconnectAttempts);
    console.log(
      `🔄 ${delay}ms 后尝试重连 (${this.reconnectAttempts + 1}/${
        this.options.maxReconnectAttempts
      })`
    );

    setTimeout(() => {
      if (this.connectionState !== "connected") {
        this.connect();
      }
    }, delay);
  }

  startHeartbeat() {
    this.heartbeatTimer = setInterval(() => {
      if (this.connectionState === "connected") {
        this.ping();
      }
    }, this.options.heartbeatInterval);
  }

  stopHeartbeat() {
    if (this.heartbeatTimer) {
      clearInterval(this.heartbeatTimer);
      this.heartbeatTimer = null;
    }
  }

  // 注册错误处理器
  onError(type, handler) {
    this.errorHandlers.set(type, handler);
  }

  // 事件发射器
  emit(event, data) {
    const handler = this.messageHandlers.get(event);
    if (handler) {
      handler(data);
    }
  }
}
```

### 3. 组件级错误边界

```jsx
// components/ErrorBoundary.jsx
import React from "react";

class ErrorBoundary extends React.Component {
  constructor(props) {
    super(props);
    this.state = { hasError: false, error: null, errorInfo: null };
  }

  static getDerivedStateFromError(error) {
    return { hasError: true };
  }

  componentDidCatch(error, errorInfo) {
    this.setState({
      error: error,
      errorInfo: errorInfo,
    });

    // 记录错误到日志服务
    console.error("组件错误:", error, errorInfo);
  }

  render() {
    if (this.state.hasError) {
      return (
        <div className="error-boundary">
          <h2>🚨 出现了一些问题</h2>
          <details style={{ whiteSpace: "pre-wrap" }}>
            <summary>错误详情</summary>
            {this.state.error && this.state.error.toString()}
            <br />
            {this.state.errorInfo.componentStack}
          </details>
          <button onClick={() => window.location.reload()}>刷新页面</button>
        </div>
      );
    }

    return this.props.children;
  }
}

export default ErrorBoundary;
```

## 🔄 状态管理建议

### 1. 使用 Redux Toolkit

```javascript
// store/agentSlice.js
import { createSlice, createAsyncThunk } from "@reduxjs/toolkit";
import { agentApi } from "../utils/agentApi";

// 异步 thunk
export const createSessionAsync = createAsyncThunk(
  "agent/createSession",
  async (_, { rejectWithValue }) => {
    try {
      return await agentApi.createSession();
    } catch (error) {
      return rejectWithValue(error.message);
    }
  }
);

export const createTaskAsync = createAsyncThunk(
  "agent/createTask",
  async ({ sessionId, message }, { rejectWithValue }) => {
    try {
      return await agentApi.createTask(sessionId, message);
    } catch (error) {
      return rejectWithValue(error.message);
    }
  }
);

const agentSlice = createSlice({
  name: "agent",
  initialState: {
    currentSession: null,
    currentTask: null,
    messages: [],
    connectionStatus: "disconnected",
    taskStatus: null,
    loading: false,
    error: null,
  },
  reducers: {
    addMessage: (state, action) => {
      state.messages.push({
        id: Date.now() + Math.random(),
        timestamp: new Date().toISOString(),
        ...action.payload,
      });
    },
    updateConnectionStatus: (state, action) => {
      state.connectionStatus = action.payload;
    },
    updateTaskStatus: (state, action) => {
      state.taskStatus = action.payload;
    },
    clearMessages: (state) => {
      state.messages = [];
    },
    clearError: (state) => {
      state.error = null;
    },
  },
  extraReducers: (builder) => {
    builder
      // 创建会话
      .addCase(createSessionAsync.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(createSessionAsync.fulfilled, (state, action) => {
        state.loading = false;
        state.currentSession = action.payload;
      })
      .addCase(createSessionAsync.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })
      // 创建任务
      .addCase(createTaskAsync.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(createTaskAsync.fulfilled, (state, action) => {
        state.loading = false;
        state.currentTask = action.payload;
      })
      .addCase(createTaskAsync.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      });
  },
});

export const {
  addMessage,
  updateConnectionStatus,
  updateTaskStatus,
  clearMessages,
  clearError,
} = agentSlice.actions;

export default agentSlice.reducer;
```

### 2. 使用 Zustand (轻量级状态管理)

```javascript
// store/agentStore.js
import { create } from "zustand";
import { agentApi } from "../utils/agentApi";

export const useAgentStore = create((set, get) => ({
  // 状态
  currentSession: null,
  currentTask: null,
  messages: [],
  connectionStatus: "disconnected",
  taskStatus: null,
  loading: false,
  error: null,

  // 动作
  addMessage: (message) =>
    set((state) => ({
      messages: [
        ...state.messages,
        {
          id: Date.now() + Math.random(),
          timestamp: new Date().toISOString(),
          ...message,
        },
      ],
    })),

  updateConnectionStatus: (status) => set({ connectionStatus: status }),

  updateTaskStatus: (status) => set({ taskStatus: status }),

  clearMessages: () => set({ messages: [] }),

  clearError: () => set({ error: null }),

  // 异步动作
  createSession: async () => {
    set({ loading: true, error: null });
    try {
      const session = await agentApi.createSession();
      set({ currentSession: session, loading: false });
      return session;
    } catch (error) {
      set({ error: error.message, loading: false });
      throw error;
    }
  },

  createTask: async (message) => {
    const { currentSession } = get();
    if (!currentSession) {
      throw new Error("没有活动会话");
    }

    set({ loading: true, error: null });
    try {
      const task = await agentApi.createTask(
        currentSession.session_id,
        message
      );
      set({ currentTask: task, loading: false });
      return task;
    } catch (error) {
      set({ error: error.message, loading: false });
      throw error;
    }
  },
}));
```

## 📱 响应式设计

### 移动端适配

```css
/* styles/responsive.css */
@media (max-width: 768px) {
  .agent-chat {
    height: 100vh;
    border-radius: 0;
    border: none;
  }

  .connection-status {
    padding: 8px 12px;
    font-size: 12px;
  }

  .task-status {
    padding: 8px 12px;
  }

  .messages-container {
    padding: 10px;
  }

  .message {
    max-width: 90%;
    padding: 8px 12px;
    font-size: 14px;
  }

  .input-area {
    padding: 10px;
    gap: 8px;
  }

  .input-area input {
    font-size: 16px; /* 防止iOS缩放 */
  }

  .input-area button {
    padding: 8px 16px;
    font-size: 14px;
  }
}

@media (max-width: 480px) {
  .status-bar {
    flex-direction: column;
    gap: 4px;
  }

  .message-time {
    font-size: 10px;
  }

  .input-area {
    flex-direction: column;
  }

  .input-area button {
    width: 100%;
  }
}
```

## 🧪 测试示例

### 1. 单元测试

```javascript
// tests/agentApi.test.js
import { describe, it, expect, vi, beforeEach } from "vitest";
import { AgentApiClient } from "../utils/agentApi";

describe("AgentApiClient", () => {
  let client;
  let mockFetch;

  beforeEach(() => {
    client = new AgentApiClient("http://localhost:8000");
    mockFetch = vi.fn();
    global.fetch = mockFetch;
  });

  it("should create session successfully", async () => {
    const mockResponse = {
      session_id: "test-session-id",
      status: "active",
      created_at: "2025-01-17T10:30:00.000Z",
    };

    mockFetch.mockResolvedValueOnce({
      ok: true,
      json: () => Promise.resolve(mockResponse),
    });

    const result = await client.createSession();

    expect(mockFetch).toHaveBeenCalledWith(
      "http://localhost:8000/api/sessions",
      expect.objectContaining({
        method: "POST",
        headers: { "Content-Type": "application/json" },
      })
    );
    expect(result).toEqual(mockResponse);
  });

  it("should handle API errors", async () => {
    mockFetch.mockResolvedValueOnce({
      ok: false,
      status: 500,
      statusText: "Internal Server Error",
      json: () => Promise.resolve({ detail: "Server error" }),
    });

    await expect(client.createSession()).rejects.toThrow("Server error");
  });
});
```

### 2. 组件测试

```jsx
// tests/AgentChat.test.jsx
import { render, screen, fireEvent, waitFor } from "@testing-library/react";
import { vi } from "vitest";
import AgentChat from "../components/AgentChat";
import * as agentApi from "../utils/agentApi";

// Mock API
vi.mock("../utils/agentApi");

describe("AgentChat", () => {
  beforeEach(() => {
    vi.clearAllMocks();

    // Mock successful API responses
    agentApi.checkHealth.mockResolvedValue({ status: "ok" });
    agentApi.createSession.mockResolvedValue({
      session_id: "test-session",
      status: "active",
    });
    agentApi.createTask.mockResolvedValue({
      task_id: "test-task",
      status: "queued",
    });
  });

  it("should initialize and show connection status", async () => {
    render(<AgentChat />);

    await waitFor(() => {
      expect(screen.getByText(/已连接/)).toBeInTheDocument();
    });
  });

  it("should send message and create task", async () => {
    render(<AgentChat />);

    await waitFor(() => {
      expect(screen.getByText(/已连接/)).toBeInTheDocument();
    });

    const input = screen.getByPlaceholderText("输入您的指令...");
    const sendButton = screen.getByText("发送");

    fireEvent.change(input, { target: { value: "测试消息" } });
    fireEvent.click(sendButton);

    await waitFor(() => {
      expect(agentApi.createTask).toHaveBeenCalledWith(
        "test-session",
        "测试消息"
      );
    });
  });
});
```

## 🚀 部署和优化

### 1. 生产环境配置

```javascript
// config/production.js
export const PRODUCTION_CONFIG = {
  API_BASE_URL: process.env.REACT_APP_API_URL || "https://your-api-domain.com",
  WS_BASE_URL: process.env.REACT_APP_WS_URL || "wss://your-api-domain.com",
  TIMEOUT: 30000,
  MAX_RECONNECT_ATTEMPTS: 5,
  HEARTBEAT_INTERVAL: 30000,
  MESSAGE_BUFFER_SIZE: 1000,
  ENABLE_DEBUG: false,
};
```

### 2. 性能优化

```javascript
// hooks/useAgentChat.js
import { useCallback, useMemo, useRef } from "react";
import { useAgentStore } from "../store/agentStore";
import { WebSocketManager } from "../utils/websocketErrorHandler";

export const useAgentChat = () => {
  const store = useAgentStore();
  const wsManagerRef = useRef(null);

  // 使用 useCallback 优化函数引用
  const sendMessage = useCallback(
    async (message) => {
      try {
        const task = await store.createTask(message);
        if (wsManagerRef.current) {
          wsManagerRef.current.subscribeToTask(task.task_id);
        }
        return task;
      } catch (error) {
        console.error("发送消息失败:", error);
        throw error;
      }
    },
    [store.createTask]
  );

  // 使用 useMemo 优化计算属性
  const messageStats = useMemo(() => {
    const messages = store.messages;
    return {
      total: messages.length,
      thinking: messages.filter((m) => m.type === "thinking").length,
      toolCalls: messages.filter((m) => m.type === "tool_call").length,
      errors: messages.filter((m) => m.type === "error").length,
    };
  }, [store.messages]);

  return {
    ...store,
    sendMessage,
    messageStats,
    wsManagerRef,
  };
};
```

## 📚 常见问题解答

### Q1: WebSocket 连接频繁断开怎么办？

**A**: 实现心跳机制和自动重连：

```javascript
// 在 WebSocketManager 中添加
startHeartbeat() {
  this.heartbeatTimer = setInterval(() => {
    if (this.isConnected) {
      this.send({ type: 'ping' });
    }
  }, 30000); // 每30秒发送心跳
}
```

### Q2: 如何处理大量消息导致的性能问题？

**A**: 实现消息分页和虚拟滚动：

```javascript
// 限制消息数量
const MAX_MESSAGES = 1000;

const addMessage = (message) => {
  setMessages((prev) => {
    const newMessages = [...prev, message];
    return newMessages.length > MAX_MESSAGES
      ? newMessages.slice(-MAX_MESSAGES)
      : newMessages;
  });
};
```

### Q3: 如何实现消息持久化？

**A**: 使用 localStorage 或 IndexedDB：

```javascript
// utils/messageStorage.js
export const saveMessages = (sessionId, messages) => {
  localStorage.setItem(`messages_${sessionId}`, JSON.stringify(messages));
};

export const loadMessages = (sessionId) => {
  const stored = localStorage.getItem(`messages_${sessionId}`);
  return stored ? JSON.parse(stored) : [];
};
```

---

## 🎯 总结

本文档提供了完整的前端集成方案，包括：

- ✅ **完整的 API 调用示例** - 涵盖所有端点的使用方法
- ✅ **WebSocket 实时通信** - 详细的连接管理和消息处理
- ✅ **React/Vue 组件示例** - 可直接使用的组件代码
- ✅ **错误处理机制** - 完善的错误处理和重连逻辑
- ✅ **状态管理方案** - Redux 和 Zustand 的使用示例
- ✅ **响应式设计** - 移动端适配和样式优化
- ✅ **测试和部署** - 单元测试和生产环境配置

通过本指南，前端开发者可以快速集成 Agent 交互功能，实现丰富的用户体验。

**🔗 相关链接**:

- [API 测试报告](./agent_interaction_test_report.md)
- [重构总结文档](./agent_interaction_refactor_summary.md)
- [在线 API 文档](http://127.0.0.1:8000/docs)
