# Python Coding Standards

## Code Style Guidelines

Follow the established patterns in the OpenManus codebase:

### Type Hints

Always use type hints for function parameters, return values, and class attributes:

```python
from typing import Dict, List, Optional, Union
from pydantic import Field

async def process_data(items: List[str]) -> Dict[str, Any]:
    # Implementation
    pass
```

### Async/Await Patterns

Use async/await for all I/O operations and agent methods:

```python
async def run(self, prompt: str) -> None:
    result = await self.llm.acomplete(prompt)
    await self.tool.execute(action)
```

### Pydantic Models

Use Pydantic for data validation and configuration:

```python
from pydantic import BaseModel, Field

class AgentConfig(BaseModel):
    name: str = Field(..., description="Agent name")
    max_steps: int = Field(20, description="Maximum execution steps")
```

### Error Handling

Implement structured error handling:

```python
from app.exceptions import CustomException
from app.logger import logger

try:
    result = await risky_operation()
except CustomException as e:
    logger.error(f"Operation failed: {e}")
    raise
```

### Logging

Use the configured logger throughout the application:

```python
from app.logger import logger

logger.info("Starting operation")
logger.warning("Potential issue detected")
logger.error("Operation failed")
```

## Project-Specific Patterns

### Agent Implementation

```python
from app.agent.base import BaseAgent
from app.tool import ToolCollection

class MyAgent(BaseAgent):
    name: str = "my_agent"
    available_tools: ToolCollection = Field(default_factory=ToolCollection)

    async def run(self, prompt: str) -> None:
        # Agent logic
        pass

    async def cleanup(self) -> None:
        # Resource cleanup
        pass
```

### Tool Implementation

```python
from app.tool.base import BaseTool

class MyTool(BaseTool):
    name: str = "my_tool"
    description: str = "Tool description"

    async def execute(self, **kwargs) -> str:
        # Tool implementation
        return "result"
```

### Configuration Access

```python
from app.config import config

# Access configuration values
model_name = config.llm.model
api_key = config.llm.api_key
workspace_path = config.workspace_root
```

## Import Organization

Organize imports in this order:

1. Standard library imports
2. Third-party imports
3. Local application imports

```python
import asyncio
from typing import Dict, List

from pydantic import BaseModel, Field
import openai

from app.agent.base import BaseAgent
from app.config import config
from app.logger import logger
```

## Documentation Standards

- Use descriptive docstrings for classes and methods
- Include type information in docstrings when helpful
- Document complex logic and business rules
- Use inline comments for clarification

## Testing Patterns

- Write async tests for async code
- Use fixtures for common setup
- Test error conditions and edge cases
- Mock external dependencies appropriately

## Best Practices

- Keep functions and methods focused and small
- Use meaningful variable and function names
- Avoid deep nesting - prefer early returns
- Handle None values explicitly
- Use context managers for resource management
- Follow the DRY (Don't Repeat Yourself) principle
  description:
  globs:
  alwaysApply: false

---
