#!/usr/bin/env python3
"""
Agent 交互 API 测试脚本

测试重构后的 Agent 交互功能，包括：
1. 创建会话和任务
2. WebSocket 实时监控
3. API 端点测试
4. 日志分析
"""

import asyncio
import websockets
import json
import requests
import time
from datetime import datetime
from typing import Dict, List, Any
import threading
import sys
import os

class AgentInteractionTester:
    def __init__(self, base_url="http://127.0.0.1:8000"):
        self.base_url = base_url
        self.session_id = None
        self.task_id = None
        self.test_results = {
            "start_time": datetime.now(),
            "api_tests": [],
            "websocket_messages": [],
            "errors": [],
            "summary": {}
        }
        
    def log(self, message: str, level: str = "INFO"):
        """记录测试日志"""
        timestamp = datetime.now().strftime("%H:%M:%S.%f")[:-3]
        print(f"[{timestamp}] [{level}] {message}")
        
    def record_api_test(self, endpoint: str, method: str, status_code: int, response_data: Any = None, error: str = None):
        """记录API测试结果"""
        self.test_results["api_tests"].append({
            "timestamp": datetime.now().isoformat(),
            "endpoint": endpoint,
            "method": method,
            "status_code": status_code,
            "response_data": response_data,
            "error": error,
            "success": status_code == 200 or status_code == 201
        })
        
    def record_websocket_message(self, message: Dict[str, Any]):
        """记录WebSocket消息"""
        self.test_results["websocket_messages"].append({
            "timestamp": datetime.now().isoformat(),
            "message": message
        })
        
    def record_error(self, error: str, context: str = ""):
        """记录错误"""
        self.test_results["errors"].append({
            "timestamp": datetime.now().isoformat(),
            "error": error,
            "context": context
        })
        self.log(f"ERROR: {error} (Context: {context})", "ERROR")

    def test_health_check(self):
        """测试健康检查端点"""
        self.log("测试健康检查端点...")
        try:
            response = requests.get(f"{self.base_url}/api/health", timeout=10)
            self.record_api_test("/api/health", "GET", response.status_code, response.json())
            
            if response.status_code == 200:
                data = response.json()
                self.log(f"服务状态: {data.get('status')}")
                self.log(f"会话数: {data.get('sessions')}, 任务数: {data.get('tasks')}")
                return True
            else:
                self.record_error(f"健康检查失败: {response.status_code}")
                return False
        except Exception as e:
            self.record_error(f"健康检查异常: {str(e)}")
            return False

    def create_session(self):
        """创建会话"""
        self.log("创建会话...")
        try:
            response = requests.post(f"{self.base_url}/api/sessions", timeout=10)
            self.record_api_test("/api/sessions", "POST", response.status_code, response.json())
            
            if response.status_code == 201:
                data = response.json()
                self.session_id = data["session_id"]
                self.log(f"会话创建成功: {self.session_id}")
                return True
            else:
                self.record_error(f"会话创建失败: {response.status_code}")
                return False
        except Exception as e:
            self.record_error(f"会话创建异常: {str(e)}")
            return False

    def create_task(self, instruction: str):
        """创建任务"""
        self.log(f"创建任务: {instruction}")
        try:
            task_data = {"message": instruction}
            response = requests.post(
                f"{self.base_url}/api/sessions/{self.session_id}/tasks",
                json=task_data,
                timeout=10
            )
            self.record_api_test(f"/api/sessions/{self.session_id}/tasks", "POST", response.status_code, response.json())
            
            if response.status_code == 201:
                data = response.json()
                self.task_id = data["task_id"]
                self.log(f"任务创建成功: {self.task_id}")
                self.log(f"任务状态: {data.get('status')}")
                return True
            else:
                self.record_error(f"任务创建失败: {response.status_code}")
                return False
        except Exception as e:
            self.record_error(f"任务创建异常: {str(e)}")
            return False

    async def monitor_websocket(self, duration: int = 60):
        """监控WebSocket消息"""
        self.log(f"开始WebSocket监控 (持续{duration}秒)...")
        
        uri = f"ws://127.0.0.1:8000/ws/sessions/{self.session_id}"
        
        try:
            async with websockets.connect(uri) as websocket:
                self.log("WebSocket连接建立成功")
                
                # 订阅任务事件
                if self.task_id:
                    await websocket.send(json.dumps({
                        "type": "subscribe_task",
                        "task_id": self.task_id
                    }))
                    self.log(f"已订阅任务事件: {self.task_id}")
                
                # 监控消息
                start_time = time.time()
                message_count = 0
                
                while time.time() - start_time < duration:
                    try:
                        # 设置超时，避免无限等待
                        message = await asyncio.wait_for(websocket.recv(), timeout=5.0)
                        data = json.loads(message)
                        message_count += 1
                        
                        self.record_websocket_message(data)
                        self.handle_websocket_message(data)
                        
                        # 如果任务完成，可以提前结束监控
                        if (data.get("type") == "task_status" and 
                            data.get("data", {}).get("status") in ["completed", "error", "cancelled"]):
                            self.log("任务执行结束，停止监控")
                            break
                            
                    except asyncio.TimeoutError:
                        # 超时是正常的，继续监控
                        continue
                    except websockets.exceptions.ConnectionClosed:
                        self.log("WebSocket连接已关闭")
                        break
                
                self.log(f"WebSocket监控结束，共接收到 {message_count} 条消息")
                
        except Exception as e:
            self.record_error(f"WebSocket监控异常: {str(e)}", "websocket_monitor")

    def handle_websocket_message(self, message: Dict[str, Any]):
        """处理WebSocket消息"""
        msg_type = message.get("type")
        data = message.get("data", {})
        timestamp = message.get("timestamp", "")
        
        if msg_type == "thinking":
            content = data.get("content", "")[:100]
            self.log(f"🤔 思考: {content}...")
            if data.get("reasoning"):
                self.log(f"   推理: {data.get('reasoning')[:80]}...")
        
        elif msg_type == "tool_call":
            tool_name = data.get("tool_name")
            status = data.get("status")
            self.log(f"🔧 工具调用: {tool_name} - {status}")
            if status == "success" and data.get("execution_time"):
                self.log(f"   执行时间: {data.get('execution_time'):.2f}秒")
        
        elif msg_type == "step_update":
            step = data.get("step")
            action = data.get("action")
            status = data.get("status")
            self.log(f"📋 步骤{step}: {action} - {status}")
            if data.get("duration"):
                self.log(f"   耗时: {data.get('duration'):.2f}秒")
        
        elif msg_type == "log":
            level = data.get("level")
            component = data.get("component")
            message_text = data.get("message")
            self.log(f"📝 [{level}] {component}: {message_text}")
        
        elif msg_type == "task_status":
            status = data.get("status")
            progress = data.get("progress_percentage", 0)
            current_step = data.get("current_step", 0)
            max_steps = data.get("max_steps", 20)
            self.log(f"📊 任务状态: {status} ({progress:.1f}%) - 步骤 {current_step}/{max_steps}")
        
        elif msg_type == "heartbeat":
            count = data.get("heartbeat_count")
            self.log(f"💓 心跳: {count}")
        
        elif msg_type == "connection":
            self.log(f"🔗 连接: {data.get('message')}")
        
        else:
            self.log(f"📨 未知消息类型: {msg_type}")

    def test_agent_detail_apis(self):
        """测试Agent详细信息API"""
        if not self.task_id:
            self.log("没有任务ID，跳过详细信息API测试")
            return
        
        self.log("测试Agent详细信息API...")
        
        # 等待一下让任务有时间执行
        time.sleep(5)
        
        # 测试获取任务执行详情
        try:
            response = requests.get(f"{self.base_url}/api/agent-details/tasks/{self.task_id}/execution")
            self.record_api_test(f"/api/agent-details/tasks/{self.task_id}/execution", "GET", response.status_code, response.json() if response.status_code == 200 else None)
            
            if response.status_code == 200:
                data = response.json()
                if data:
                    self.log(f"执行详情: 总步骤 {len(data.get('steps', []))}, 总耗时 {data.get('total_duration', 0):.2f}秒")
                else:
                    self.log("执行详情为空")
        except Exception as e:
            self.record_error(f"获取执行详情异常: {str(e)}")
        
        # 测试获取任务步骤
        try:
            response = requests.get(f"{self.base_url}/api/agent-details/tasks/{self.task_id}/steps")
            self.record_api_test(f"/api/agent-details/tasks/{self.task_id}/steps", "GET", response.status_code, response.json() if response.status_code == 200 else None)
            
            if response.status_code == 200:
                data = response.json()
                self.log(f"步骤汇总: 总步骤 {data.get('total_steps', 0)}")
        except Exception as e:
            self.record_error(f"获取步骤汇总异常: {str(e)}")
        
        # 测试获取工具调用统计
        try:
            response = requests.get(f"{self.base_url}/api/agent-details/tasks/{self.task_id}/tools")
            self.record_api_test(f"/api/agent-details/tasks/{self.task_id}/tools", "GET", response.status_code, response.json() if response.status_code == 200 else None)
            
            if response.status_code == 200:
                data = response.json()
                self.log(f"工具调用统计: 总调用 {data.get('total_tool_calls', 0)} 次")
        except Exception as e:
            self.record_error(f"获取工具调用统计异常: {str(e)}")

    def test_session_summary(self):
        """测试会话汇总"""
        if not self.session_id:
            return
        
        self.log("测试会话汇总API...")
        try:
            response = requests.get(f"{self.base_url}/api/agent-details/sessions/{self.session_id}/summary")
            self.record_api_test(f"/api/agent-details/sessions/{self.session_id}/summary", "GET", response.status_code, response.json() if response.status_code == 200 else None)
            
            if response.status_code == 200:
                data = response.json()
                self.log(f"会话汇总: 总任务 {data.get('total_tasks', 0)}, 完成 {data.get('completed_tasks', 0)}")
        except Exception as e:
            self.record_error(f"获取会话汇总异常: {str(e)}")

    def generate_test_report(self):
        """生成测试报告"""
        end_time = datetime.now()
        duration = (end_time - self.test_results["start_time"]).total_seconds()
        
        # 统计结果
        api_success_count = sum(1 for test in self.test_results["api_tests"] if test["success"])
        api_total_count = len(self.test_results["api_tests"])
        websocket_message_count = len(self.test_results["websocket_messages"])
        error_count = len(self.test_results["errors"])
        
        # 按消息类型统计WebSocket消息
        message_types = {}
        for msg in self.test_results["websocket_messages"]:
            msg_type = msg["message"].get("type", "unknown")
            message_types[msg_type] = message_types.get(msg_type, 0) + 1
        
        self.test_results["summary"] = {
            "test_duration": duration,
            "end_time": end_time.isoformat(),
            "api_tests": {
                "total": api_total_count,
                "success": api_success_count,
                "failure": api_total_count - api_success_count,
                "success_rate": (api_success_count / api_total_count * 100) if api_total_count > 0 else 0
            },
            "websocket": {
                "total_messages": websocket_message_count,
                "message_types": message_types
            },
            "errors": error_count
        }
        
        return self.test_results

async def main():
    """主测试函数"""
    tester = AgentInteractionTester()
    
    print("=" * 60)
    print("Agent 交互 API 测试开始")
    print("=" * 60)
    
    # 1. 健康检查
    if not tester.test_health_check():
        print("健康检查失败，退出测试")
        return
    
    # 2. 创建会话
    if not tester.create_session():
        print("会话创建失败，退出测试")
        return
    
    # 3. 创建任务
    instruction = "用python帮我写数组去重 放到workspace工作目录"
    if not tester.create_task(instruction):
        print("任务创建失败，退出测试")
        return
    
    # 4. 启动WebSocket监控（在后台运行）
    websocket_task = asyncio.create_task(tester.monitor_websocket(120))  # 监控2分钟
    
    # 5. 等待一段时间让任务执行
    await asyncio.sleep(10)
    
    # 6. 测试详细信息API
    tester.test_agent_detail_apis()
    
    # 7. 测试会话汇总
    tester.test_session_summary()
    
    # 8. 等待WebSocket监控完成
    try:
        await asyncio.wait_for(websocket_task, timeout=130)
    except asyncio.TimeoutError:
        tester.log("WebSocket监控超时")
        websocket_task.cancel()
    
    # 9. 生成测试报告
    test_results = tester.generate_test_report()
    
    print("\n" + "=" * 60)
    print("测试完成，生成报告...")
    print("=" * 60)
    
    # 保存测试结果到文件
    with open("test_results.json", "w", encoding="utf-8") as f:
        json.dump(test_results, f, ensure_ascii=False, indent=2, default=str)
    
    print(f"测试结果已保存到 test_results.json")
    
    # 显示简要统计
    summary = test_results["summary"]
    print(f"\n📊 测试统计:")
    print(f"  测试时长: {summary['test_duration']:.2f}秒")
    print(f"  API测试: {summary['api_tests']['success']}/{summary['api_tests']['total']} 成功 ({summary['api_tests']['success_rate']:.1f}%)")
    print(f"  WebSocket消息: {summary['websocket']['total_messages']} 条")
    print(f"  错误数量: {summary['errors']}")
    
    if summary['websocket']['message_types']:
        print(f"\n📡 WebSocket消息类型分布:")
        for msg_type, count in summary['websocket']['message_types'].items():
            print(f"  {msg_type}: {count} 条")

if __name__ == "__main__":
    asyncio.run(main())
