"""
Web API 配置模块
"""

from typing import Any, Dict  # type: ignore

from pydantic import BaseModel  # type: ignore


class WebAPIConfig(BaseModel):
    """Web API 配置类（简化版）"""

    host: str = "0.0.0.0"
    port: int = 8000
    debug: bool = False  # 禁用reload模式以修复MCP连接问题
    title: str = "RPA Agent API (Simple)"
    description: str = "RPA代理简化版API"
    version: str = "1.0.0"
    openapi_url: str = "/openapi.json"
    docs_url: str = "/docs"
    redoc_url: str = "/redoc"

    # 资源限制（简化）
    max_sessions: int = 50
    max_tasks_per_session: int = 5
    task_timeout: int = 300
    session_max_age_hours: int = 24

    # 原有数据库配置（保留兼容性）
    db_host: str = "localhost"
    db_port: int = 3306
    db_user: str = "root"
    db_password: str = "mysql123456"
    db_name: str = "rpa-agent"

    @property
    def database_url(self) -> str:
        """获取数据库URL"""
        return f"mysql+pymysql://{self.db_user}:{self.db_password}@{self.db_host}:{self.db_port}/{self.db_name}"


# 默认配置
config = WebAPIConfig()
