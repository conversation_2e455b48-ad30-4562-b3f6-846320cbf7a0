# Web API 任务状态持久化修复

## 🐛 问题描述

用户反馈 Web API 的任务状态检查接口存在问题：

- 任务启动后无法追踪状态
- 服务重启后所有会话和任务数据丢失
- 前端收到 404 错误，提示任务不存在

## 🔍 根本原因

1. **内存存储问题**：`SimpleStateManager` 使用纯内存存储，没有持久化
2. **热重载影响**：文件修改触发服务重启，内存数据全部丢失
3. **状态不一致**：任务执行过程中服务重启导致状态管理器重置

从日志分析：

```
# 任务创建成功
2025-07-16 22:03:52.441 | INFO | 创建任务 847d42f8-6768-49cc-90c4-83a493dbeb96 在会话 12877814-2172-4b12-a2c0-8f5061227a99

# 任务开始执行
2025-07-16 22:03:52.490 | INFO | 启动任务执行: 847d42f8-6768-49cc-90c4-83a493dbeb96

# 服务重启（文件修改触发）
WARNING: WatchFiles detected changes in 'deduplicate.py'. Reloading...

# 重启后数据丢失
INFO: 127.0.0.1:51410 - "GET /api/sessions/12877814-2172-4b12-a2c0-8f5061227a99/tasks/847d42f8-6768-49cc-90c4-83a493dbeb96 HTTP/1.1" 404 Not Found
```

## 🛠️ 解决方案

### 1. 添加持久化存储

**修改文件：** `web_api/services/state_manager.py`

**主要改进：**

- 添加 JSON 文件持久化存储会话和任务数据
- 数据目录：`PROJECT_ROOT/data/`
- 持久化文件：
  - `sessions.json` - 会话数据
  - `tasks.json` - 任务数据

**新增方法：**

```python
def _load_data(self):
    """从文件加载持久化数据"""

def _save_data(self):
    """保存数据到文件"""

async def initialize_after_startup(self):
    """启动后初始化，处理重新排队的任务"""

async def _requeue_pending_tasks(self):
    """重新排队未完成的任务"""
```

### 2. 自动任务恢复

**关键逻辑：**

- 服务启动时自动加载持久化数据
- 识别未完成的任务（状态为 `queued` 或 `running`）
- 自动重新排队未完成的任务
- 重置运行中任务的状态为 `queued`

### 3. 实时数据同步

**触发条件：**

- 创建会话/任务时立即保存
- 重要状态更新时立即保存（`status`、`result`、`error_message`）
- 清理过期数据时保存

### 4. 后台服务集成

**修改文件：** `web_api/services/background.py`

**改进：**

```python
async def start_background_tasks():
    """启动所有后台任务"""
    logger.info("启动后台服务")

    # 初始化状态管理器（处理重新排队）
    await state_manager.initialize_after_startup()

    # 创建后台任务...
```

### 5. WebSocket 连接优化

**修改文件：** `web_api/routers/websocket.py`

**改进：**

- 更好的会话验证
- 标准化错误代码
- 增强日志记录

## 📊 技术细节

### 数据序列化

**时间处理：**

```python
# 保存时转换为ISO格式
task_copy['created_at'] = task_copy['created_at'].isoformat()

# 加载时转换回datetime对象
task['created_at'] = datetime.fromisoformat(task['created_at'])
```

### 容错机制

1. **启动时容错**：如果持久化文件损坏，系统仍能正常启动
2. **保存时容错**：保存失败不影响程序运行，仅记录错误日志
3. **异步处理**：重新排队任务采用异步处理，避免阻塞启动

### 性能优化

1. **选择性保存**：只在重要状态变更时触发保存
2. **异步 IO**：文件操作不阻塞主流程
3. **内存优先**：仍使用内存作为主要存储，文件仅作备份

## 🎯 修复效果

### 修复前

- ❌ 服务重启后任务丢失
- ❌ 无法追踪长时间运行的任务
- ❌ 前端收到 404 错误
- ❌ 数据完全依赖内存

### 修复后

- ✅ 服务重启后自动恢复会话和任务
- ✅ 长期任务可持续追踪
- ✅ 前端能正确获取任务状态
- ✅ 数据持久化保证不丢失
- ✅ 未完成任务自动重新排队
- ✅ 完整的状态生命周期管理

## 🔄 升级步骤

1. **备份现有数据**（如有重要任务正在运行）
2. **部署新代码**
3. **重启服务**
4. **验证数据目录创建**：检查 `PROJECT_ROOT/data/` 目录
5. **测试功能**：创建任务并验证持久化

## 📝 后续建议

1. **考虑数据库**：长期建议使用 SQLite 或其他数据库替代 JSON 文件
2. **定期清理**：增强过期数据清理机制
3. **监控告警**：添加数据持久化失败的监控
4. **备份策略**：定期备份持久化文件

## 🧪 测试验证

推荐测试场景：

1. 创建任务 → 重启服务 → 验证任务状态可查询
2. 长时间运行任务 → 中途重启 → 验证任务继续执行
3. WebSocket 连接 → 重启服务 → 验证重连后状态同步

---

**修复时间：** 2025-01-16
**影响范围：** Web API 任务管理系统
**修复类型：** 功能增强 + Bug 修复
