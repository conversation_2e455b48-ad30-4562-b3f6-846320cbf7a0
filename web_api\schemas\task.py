"""
任务相关的数据模型
"""

from typing import List, Optional, Any
from datetime import datetime
from pydantic import BaseModel, Field
from enum import Enum


class TaskStatus(str, Enum):
    """任务状态枚举"""
    PENDING = "pending"
    QUEUED = "queued"
    RUNNING = "running"
    COMPLETED = "completed"
    ERROR = "error"
    CANCELLED = "cancelled"


class StepStatus(str, Enum):
    """步骤状态枚举"""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    ERROR = "error"


class TaskCreate(BaseModel):
    """创建任务请求模型"""
    message: str = Field(..., description="任务描述/用户输入")


class TaskResponse(BaseModel):
    """任务响应模型"""
    task_id: str = Field(..., description="任务ID")
    session_id: str = Field(..., description="会话ID")
    message: str = Field(..., description="任务描述")
    status: TaskStatus = Field(..., description="任务状态")
    current_step: int = Field(default=0, description="当前步骤")
    max_steps: int = Field(default=20, description="最大步骤数")
    created_at: datetime = Field(..., description="创建时间")
    start_time: Optional[datetime] = Field(None, description="开始时间")
    end_time: Optional[datetime] = Field(None, description="结束时间")
    result: Optional[str] = Field(None, description="任务结果")
    error_message: Optional[str] = Field(None, description="错误信息")
    progress_percentage: Optional[float] = Field(None, description="进度百分比")


class TaskList(BaseModel):
    """任务列表响应模型"""
    tasks: List[TaskResponse] = Field(..., description="任务列表")
    total: int = Field(..., description="任务总数")


class TaskCancel(BaseModel):
    """取消任务响应模型"""
    task_id: str = Field(..., description="任务ID")
    cancelled: bool = Field(..., description="是否成功取消")
    message: str = Field(..., description="取消结果信息")


# WebSocket 消息模型
class WebSocketMessage(BaseModel):
    """WebSocket 消息模型"""
    type: str = Field(..., description="消息类型: task_status|step_update|tool_call|thinking|log|error|heartbeat")
    timestamp: str = Field(..., description="时间戳")
    data: dict = Field(..., description="消息数据")


class TaskStatusData(BaseModel):
    """任务状态更新数据"""
    task_id: str = Field(..., description="任务ID")
    status: str = Field(..., description="任务状态")
    current_step: Optional[int] = Field(None, description="当前步骤")
    max_steps: Optional[int] = Field(None, description="最大步骤数")
    message: Optional[str] = Field(None, description="状态消息")
    result: Optional[str] = Field(None, description="执行结果")
    error: Optional[str] = Field(None, description="错误信息")
    progress_percentage: Optional[float] = Field(None, description="进度百分比")


class StepUpdateData(BaseModel):
    """步骤更新数据"""
    task_id: str = Field(..., description="任务ID")
    step: int = Field(..., description="步骤编号")
    action: str = Field(..., description="执行动作")
    status: str = Field(..., description="步骤状态: pending|running|completed|error")
    thoughts: Optional[str] = Field(None, description="思考内容")
    tool_name: Optional[str] = Field(None, description="使用的工具名称")
    tool_input: Optional[dict] = Field(None, description="工具输入")
    tool_output: Optional[str] = Field(None, description="工具输出")
    duration: Optional[float] = Field(None, description="步骤执行时间(秒)")
    observations: Optional[List[str]] = Field(None, description="观察结果")


# 新增：详细的WebSocket消息数据模型

class ThinkingData(BaseModel):
    """思考过程数据"""
    task_id: str = Field(..., description="任务ID")
    step: int = Field(..., description="当前步骤")
    content: str = Field(..., description="思考内容")
    reasoning: Optional[str] = Field(None, description="推理过程")
    decision: Optional[str] = Field(None, description="决策结果")
    context: Optional[dict] = Field(None, description="上下文信息")


class ToolCallData(BaseModel):
    """工具调用数据"""
    task_id: str = Field(..., description="任务ID")
    step: int = Field(..., description="当前步骤")
    tool_name: str = Field(..., description="工具名称")
    tool_id: str = Field(..., description="工具调用ID")
    status: str = Field(..., description="调用状态: start|success|error")
    arguments: Optional[dict] = Field(None, description="工具参数")
    result: Optional[str] = Field(None, description="工具执行结果")
    error: Optional[str] = Field(None, description="错误信息")
    execution_time: Optional[float] = Field(None, description="执行时间(秒)")


class LogData(BaseModel):
    """日志数据"""
    task_id: Optional[str] = Field(None, description="任务ID")
    level: str = Field(..., description="日志级别: DEBUG|INFO|WARNING|ERROR")
    component: str = Field(..., description="组件名称: agent|tool|system")
    message: str = Field(..., description="日志消息")
    context: Optional[dict] = Field(None, description="上下文信息")


class HeartbeatData(BaseModel):
    """心跳数据"""
    task_id: str = Field(..., description="任务ID")
    status: str = Field(..., description="任务状态")
    current_step: int = Field(..., description="当前步骤")
    message: str = Field(..., description="心跳消息")
    heartbeat_count: int = Field(..., description="心跳计数")
    uptime: Optional[float] = Field(None, description="运行时间(秒)")


class MemoryStateData(BaseModel):
    """内存状态数据"""
    task_id: str = Field(..., description="任务ID")
    messages_count: int = Field(..., description="消息数量")
    context_length: int = Field(..., description="上下文长度")
    memory_usage: Optional[dict] = Field(None, description="内存使用情况")
    last_message_preview: Optional[str] = Field(None, description="最后消息预览")
