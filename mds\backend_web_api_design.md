# RPA 代理后端 Web API 设计方案

## 📋 项目背景

将基于命令行的 RPA 代理交互转换为 Web API 模式，支持前端页面交互。需要保持原有的工具调用能力、实时反馈和状态管理功能。

## 🎯 核心设计目标

1. **实时通信**: 支持 WebSocket/SSE 实时推送执行状态和日志
2. **状态管理**: 维护会话状态和任务执行进度
3. **工具透明**: 完整展示工具调用过程和结果
4. **异步执行**: 支持长时间任务的异步处理
5. **错误处理**: 完善的错误处理和恢复机制

## 🔧 技术架构设计

### 核心组件

```
Web API层 ──→ 任务管理器 ──→ Agent执行器 ──→ 工具集合
    │              │              │
    ├── WebSocket   ├── 状态存储    ├── 实时日志
    ├── RESTful     ├── 任务队列    └── 工具调用
    └── SSE         └── 会话管理
```

### 技术栈

- **Web 框架**: FastAPI (已有)
- **实时通信**: WebSocket + SSE
- **状态存储**: Redis / 内存存储
- **任务队列**: asyncio.Queue / Celery
- **日志系统**: 扩展现有 logger

## 📡 API 接口设计

### 1. RESTful API

#### 1.1 会话管理

```http
POST /api/sessions
# 创建新会话
Response: {
  "session_id": "uuid",
  "status": "created",
  "created_at": "2025-01-16T10:00:00Z"
}

GET /api/sessions/{session_id}
# 获取会话信息
Response: {
  "session_id": "uuid",
  "status": "active|completed|error",
  "current_task": "task_id",
  "created_at": "timestamp",
  "updated_at": "timestamp"
}

DELETE /api/sessions/{session_id}
# 关闭会话
```

#### 1.2 任务管理

```http
POST /api/sessions/{session_id}/tasks
# 创建新任务
Request: {
  "message": "用户输入的任务描述",
  "config": {
    "max_steps": 20,
    "timeout": 300
  }
}
Response: {
  "task_id": "uuid",
  "status": "queued",
  "created_at": "timestamp"
}

GET /api/sessions/{session_id}/tasks/{task_id}
# 获取任务状态
Response: {
  "task_id": "uuid",
  "status": "queued|running|completed|error|cancelled",
  "current_step": 3,
  "max_steps": 20,
  "start_time": "timestamp",
  "end_time": "timestamp",
  "result": "最终结果"
}

POST /api/sessions/{session_id}/tasks/{task_id}/cancel
# 取消任务
```

#### 1.3 工具管理

```http
GET /api/tools
# 获取可用工具列表
Response: {
  "tools": [
    {
      "name": "python_execute",
      "description": "执行Python代码",
      "category": "core",
      "parameters": {...}
    }
  ]
}

GET /api/tools/{tool_name}
# 获取特定工具详情
```

#### 1.4 文件管理

```http
GET /api/sessions/{session_id}/files
# 获取会话相关文件
POST /api/sessions/{session_id}/files
# 上传文件
GET /api/sessions/{session_id}/files/{file_id}
# 下载文件
```

### 2. WebSocket API

#### 连接端点

```
WS /ws/sessions/{session_id}
```

#### 消息格式

```typescript
// 客户端发送
{
  "type": "task_create|task_cancel|user_input",
  "data": {
    "message": "用户输入",
    "task_id": "uuid" // 可选
  }
}

// 服务端推送
{
  "type": "task_status|step_update|tool_call|log|error",
  "timestamp": "2025-01-16T10:00:00Z",
  "data": {
    // 具体数据结构见下方
  }
}
```

#### 实时事件类型

**1. 任务状态更新**

```json
{
  "type": "task_status",
  "data": {
    "task_id": "uuid",
    "status": "running",
    "current_step": 1,
    "max_steps": 20
  }
}
```

**2. 执行步骤更新**

```json
{
  "type": "step_update",
  "data": {
    "task_id": "uuid",
    "step": 1,
    "action": "思考中...",
    "thoughts": "✨ Manus's thoughts: 我需要创建一个Python脚本来实现去重功能"
  }
}
```

**3. 工具调用事件**

```json
{
  "type": "tool_call",
  "data": {
    "task_id": "uuid",
    "step": 1,
    "tool_name": "str_replace_editor",
    "status": "start|success|error",
    "arguments": {...},
    "result": "工具执行结果",
    "execution_time": 1.2
  }
}
```

**4. 日志事件**

```json
{
  "type": "log",
  "data": {
    "level": "INFO|WARNING|ERROR",
    "message": "日志内容",
    "component": "agent|tool|system"
  }
}
```

### 3. SSE API (服务端推送事件)

#### 连接端点

```
GET /api/sessions/{session_id}/events
```

#### 事件格式

```
event: task_status
data: {"task_id": "uuid", "status": "running"}

event: tool_call
data: {"tool_name": "python_execute", "status": "success"}

event: log
data: {"level": "INFO", "message": "任务执行完成"}
```

## 🏗️ 实现架构

### 1. 会话管理器 (SessionManager)

```python
class SessionManager:
    async def create_session() -> Session
    async def get_session(session_id) -> Session
    async def close_session(session_id)
    async def cleanup_expired_sessions()
```

### 2. 任务管理器 (TaskManager)

```python
class TaskManager:
    async def create_task(session_id, message) -> Task
    async def execute_task(task_id)
    async def cancel_task(task_id)
    async def get_task_status(task_id) -> TaskStatus
```

### 3. 实时事件管理器 (EventManager)

```python
class EventManager:
    async def subscribe(session_id, websocket)
    async def publish(session_id, event)
    async def broadcast(event)
```

### 4. Agent 适配器 (AgentAdapter)

```python
class AgentAdapter:
    def __init__(self, event_manager):
        self.agent = ManusAgent()
        self.event_manager = event_manager

    async def execute_with_events(task):
        # 包装原有agent，添加事件推送
```

## 📊 数据模型

### Session (会话)

```python
class Session(BaseModel):
    session_id: str
    status: Literal["active", "completed", "error"]
    created_at: datetime
    updated_at: datetime
    current_task: Optional[str] = None
    config: dict = {}
```

### Task (任务)

```python
class Task(BaseModel):
    task_id: str
    session_id: str
    status: Literal["queued", "running", "completed", "error", "cancelled"]
    message: str
    current_step: int = 0
    max_steps: int = 20
    start_time: Optional[datetime] = None
    end_time: Optional[datetime] = None
    result: Optional[str] = None
    error_message: Optional[str] = None
```

### ToolCall (工具调用)

```python
class ToolCall(BaseModel):
    task_id: str
    step: int
    tool_name: str
    arguments: dict
    status: Literal["start", "success", "error"]
    result: Optional[Any] = None
    error: Optional[str] = None
    execution_time: float
    timestamp: datetime
```

## 🔐 安全考虑

### 1. 认证授权

- API Key 认证
- 会话 token 验证
- 权限控制

### 2. 资源限制

- 并发会话数量限制
- 单会话任务数量限制
- 执行时间限制
- 文件大小限制

### 3. 安全隔离

- 文件访问路径限制
- Python 执行沙箱
- 网络访问控制

## 📈 性能优化

### 1. 连接管理

- WebSocket 连接池
- 心跳检测
- 自动重连机制

### 2. 缓存策略

- 工具定义缓存
- 会话状态缓存
- 文件内容缓存

### 3. 异步处理

- 任务队列
- 并发控制
- 资源回收

## 🔍 监控和日志

### 1. 指标监控

- API 响应时间
- WebSocket 连接数
- 任务执行成功率
- 工具调用统计

### 2. 日志设计

- 结构化日志
- 日志级别控制
- 实时日志推送
- 日志持久化存储

### 3. 健康检查

- API 健康状态
- 依赖服务检查
- 资源使用监控

## 🚀 部署配置

### 1. 环境变量

```env
# Web API配置
WEB_API_HOST=0.0.0.0
WEB_API_PORT=8000
WEB_API_DEBUG=false

# Redis配置 (可选)
REDIS_URL=redis://localhost:6379

# 安全配置
API_KEY=your-secret-key
SESSION_TIMEOUT=3600

# 资源限制
MAX_CONCURRENT_SESSIONS=100
MAX_TASKS_PER_SESSION=10
TASK_TIMEOUT=300
```

### 2. 启动脚本优化

```python
# run_web_api.py 增强版
import uvicorn
from web_api.main import create_app

if __name__ == "__main__":
    app = create_app()
    uvicorn.run(
        app,
        host=config.host,
        port=config.port,
        reload=config.debug,
        ws_ping_interval=30,
        ws_ping_timeout=10
    )
```

## 📋 开发计划

### Phase 1: 基础 API (1-2 周)

- [ ] RESTful API 实现
- [ ] 基础会话管理
- [ ] 任务创建和状态查询

### Phase 2: 实时通信 (1-2 周)

- [ ] WebSocket 连接管理
- [ ] SSE 事件推送
- [ ] Agent 执行状态实时同步

### Phase 3: 工具集成 (1 周)

- [ ] 工具调用事件推送
- [ ] 文件管理接口
- [ ] 错误处理和恢复

### Phase 4: 优化和监控 (1 周)

- [ ] 性能优化
- [ ] 监控和日志
- [ ] 安全加固

这个设计方案确保了与现有系统的兼容性，同时为前端提供了丰富的实时交互能力。
