# MCP动态管理API使用示例

## 🚀 快速开始

### 1. 启动应用
```bash
# 启动完整应用(包含Web API)
python run_with_web_api.py

# 或者仅启动Web API
python run_web_api.py
```

### 2. 访问API文档
- Swagger UI: http://localhost:8000/docs
- ReDoc: http://localhost:8000/redoc

## 📋 API使用示例

### 1. 获取所有MCP服务器列表

**请求**:
```http
GET /api/mcp/servers
```

**响应**:
```json
{
  "servers": [
    {
      "server_id": "playwright",
      "name": "Playwright MCP",
      "type": "stdio",
      "description": "Playwright自动化工具",
      "enabled": true,
      "status": "connected",
      "command": "node",
      "args": ["external/playwright-mcp/cli.js", "--browser=chrome"],
      "url": null,
      "tools": [
        {
          "name": "mcp_playwright_browser_tab_new",
          "description": "创建新的浏览器标签页",
          "parameters": {...}
        }
      ],
      "error_message": null,
      "connected_at": "2024-01-15T10:30:00"
    }
  ],
  "total": 1
}
```

### 2. 创建新的MCP服务器配置

#### 创建stdio类型服务
**请求**:
```http
POST /api/mcp/servers
Content-Type: application/json

{
  "name": "Playwright MCP",
  "type": "stdio",
  "description": "Playwright自动化工具",
  "enabled": true,
  "command": "node",
  "args": [
    "external/playwright-mcp/cli.js",
    "--browser=chrome",
    "--no-sandbox"
  ]
}
```

#### 创建sse类型服务
**请求**:
```http
POST /api/mcp/servers
Content-Type: application/json

{
  "name": "Remote MCP Service",
  "type": "sse",
  "description": "远程MCP服务",
  "enabled": true,
  "url": "http://localhost:3000/sse"
}
```

**响应**:
```json
{
  "server_id": "playwright_mcp",
  "name": "Playwright MCP",
  "type": "stdio",
  "description": "Playwright自动化工具",
  "enabled": true,
  "status": "disconnected",
  "command": "node",
  "args": ["external/playwright-mcp/cli.js", "--browser=chrome", "--no-sandbox"],
  "url": null,
  "tools": [],
  "error_message": null,
  "connected_at": null
}
```

### 3. 连接到MCP服务器

**请求**:
```http
POST /api/mcp/servers/playwright_mcp/connect
Content-Type: application/json

{
  "force": false
}
```

**响应**:
```json
{
  "success": true,
  "message": "成功连接到服务器 playwright_mcp",
  "server_info": {
    "server_id": "playwright_mcp",
    "name": "Playwright MCP",
    "type": "stdio",
    "status": "connected",
    "tools": [
      {
        "name": "mcp_playwright_mcp_browser_tab_new",
        "description": "创建新的浏览器标签页",
        "parameters": {...}
      }
    ],
    "connected_at": "2024-01-15T10:35:00"
  }
}
```

### 4. 获取服务器状态

**请求**:
```http
GET /api/mcp/servers/playwright_mcp/status
```

**响应**:
```json
{
  "server_id": "playwright_mcp",
  "status": "connected",
  "tools_count": 5,
  "error_message": null,
  "uptime": 300
}
```

### 5. 断开MCP服务器连接

**请求**:
```http
POST /api/mcp/servers/playwright_mcp/disconnect
```

**响应**:
```json
{
  "success": true,
  "message": "成功断开与服务器 playwright_mcp 的连接"
}
```

### 6. 更新MCP服务器配置

**请求**:
```http
PUT /api/mcp/servers/playwright_mcp
Content-Type: application/json

{
  "description": "更新后的Playwright自动化工具",
  "enabled": false,
  "args": [
    "external/playwright-mcp/cli.js",
    "--browser=firefox"
  ]
}
```

**响应**:
```json
{
  "server_id": "playwright_mcp",
  "name": "Playwright MCP",
  "type": "stdio",
  "description": "更新后的Playwright自动化工具",
  "enabled": false,
  "status": "disconnected",
  "command": "node",
  "args": ["external/playwright-mcp/cli.js", "--browser=firefox"],
  "tools": [],
  "error_message": null
}
```

### 7. 删除MCP服务器配置

**请求**:
```http
DELETE /api/mcp/servers/playwright_mcp
```

**响应**:
```json
{
  "success": true,
  "message": "服务器 playwright_mcp 已成功删除"
}
```

## 🔧 错误处理示例

### 1. 服务器不存在
**请求**:
```http
GET /api/mcp/servers/nonexistent
```

**响应**:
```json
{
  "detail": "服务器 nonexistent 不存在"
}
```
状态码: 404

### 2. 连接失败
**请求**:
```http
POST /api/mcp/servers/invalid_server/connect
```

**响应**:
```json
{
  "success": false,
  "message": "连接到服务器 invalid_server 失败: Server command not found",
  "server_info": {...}
}
```
状态码: 200 (业务错误，非HTTP错误)

### 3. 配置验证失败
**请求**:
```http
POST /api/mcp/servers
Content-Type: application/json

{
  "name": "Invalid Server",
  "type": "stdio"
  // 缺少必需的command参数
}
```

**响应**:
```json
{
  "detail": [
    {
      "loc": ["body", "command"],
      "msg": "stdio连接类型必须提供command",
      "type": "value_error"
    }
  ]
}
```
状态码: 422

## 🐍 Python客户端示例

```python
import requests
import json

# API基础URL
BASE_URL = "http://localhost:8000/api/mcp"

class MCPClient:
    def __init__(self, base_url=BASE_URL):
        self.base_url = base_url
    
    def list_servers(self):
        """获取所有服务器列表"""
        response = requests.get(f"{self.base_url}/servers")
        return response.json()
    
    def create_server(self, config):
        """创建新服务器"""
        response = requests.post(
            f"{self.base_url}/servers",
            json=config
        )
        return response.json()
    
    def connect_server(self, server_id, force=False):
        """连接服务器"""
        response = requests.post(
            f"{self.base_url}/servers/{server_id}/connect",
            json={"force": force}
        )
        return response.json()
    
    def disconnect_server(self, server_id):
        """断开服务器"""
        response = requests.post(
            f"{self.base_url}/servers/{server_id}/disconnect"
        )
        return response.json()
    
    def delete_server(self, server_id):
        """删除服务器"""
        response = requests.delete(
            f"{self.base_url}/servers/{server_id}"
        )
        return response.json()

# 使用示例
client = MCPClient()

# 创建Playwright服务
playwright_config = {
    "name": "Playwright",
    "type": "stdio",
    "description": "Playwright自动化工具",
    "command": "node",
    "args": ["external/playwright-mcp/cli.js", "--browser=chrome"]
}

# 创建并连接
server = client.create_server(playwright_config)
print(f"Created server: {server['server_id']}")

result = client.connect_server(server['server_id'])
print(f"Connection result: {result['success']}")

# 查看所有服务器
servers = client.list_servers()
print(f"Total servers: {servers['total']}")
```

## 📝 注意事项

1. **异步操作**: 连接/断开操作是异步的，可能需要一些时间
2. **状态检查**: 建议在操作后检查服务器状态确认结果
3. **错误处理**: 注意区分HTTP错误和业务逻辑错误
4. **配置验证**: 创建服务器时确保提供正确的参数
5. **资源清理**: 删除服务器会自动断开连接并清理资源
