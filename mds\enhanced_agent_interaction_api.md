# 增强的 Agent 交互 API 文档

## 📋 概述

本文档描述了重构后的 RPA Agent Web API，新增了详细的 Agent 执行过程监控和交互功能。现在可以实时获取 Agent 的思考过程、工具调用详情、执行步骤等详细信息。

## 🌐 服务信息

- **基础 URL**: `http://127.0.0.1:8000`
- **API 文档**: `http://127.0.0.1:8000/docs`
- **WebSocket**: `ws://127.0.0.1:8000/ws/sessions/{session_id}`

## 🚀 新增功能特性

### 1. 详细的 Agent 执行监控

- **思考过程追踪**: 实时获取 Agent 的思考内容和推理过程
- **工具调用监控**: 详细记录每个工具的调用参数、执行结果和耗时
- **步骤级别跟踪**: 精确到每个执行步骤的状态和持续时间
- **执行日志记录**: 完整的执行日志，包括不同级别的日志信息

### 2. 增强的 WebSocket 实时通信

- **thinking**: Agent 思考过程推送
- **tool_call**: 工具调用状态推送
- **step_update**: 执行步骤更新推送
- **log**: 执行日志推送
- **heartbeat**: 任务心跳信息

### 3. 新的 API 端点

- 获取任务详细执行信息
- 获取任务执行日志
- 获取任务步骤汇总
- 获取工具调用统计
- 获取会话执行汇总

## 📡 WebSocket 消息类型详解

### 1. 思考过程消息 (thinking)

```json
{
  "type": "thinking",
  "timestamp": "2025-01-17T10:30:00.000Z",
  "data": {
    "task_id": "uuid",
    "step": 1,
    "content": "我需要分析用户的请求，创建一个Python数组去重函数...",
    "reasoning": "用户要求保持原有顺序，所以不能使用set()方法",
    "decision": "使用列表推导式配合not in判断",
    "context": {
      "current_step": 1,
      "max_steps": 20,
      "will_act": true
    }
  }
}
```

### 2. 工具调用消息 (tool_call)

```json
{
  "type": "tool_call",
  "timestamp": "2025-01-17T10:30:15.000Z",
  "data": {
    "task_id": "uuid",
    "step": 1,
    "tool_name": "str_replace_editor",
    "tool_id": "tool-uuid",
    "status": "start|success|error",
    "arguments": {
      "command": "str_replace",
      "path": "array_dedup.py",
      "old_str": "...",
      "new_str": "..."
    },
    "result": "文件编辑成功",
    "execution_time": 1.23
  }
}
```

### 3. 步骤更新消息 (step_update)

```json
{
  "type": "step_update",
  "timestamp": "2025-01-17T10:30:30.000Z",
  "data": {
    "task_id": "uuid",
    "step": 2,
    "action": "执行Python代码",
    "status": "running|completed|error",
    "thoughts": "正在测试数组去重函数...",
    "tool_name": "python_execute",
    "duration": 2.45,
    "observations": ["函数执行成功", "测试用例通过"]
  }
}
```

### 4. 执行日志消息 (log)

```json
{
  "type": "log",
  "timestamp": "2025-01-17T10:30:45.000Z",
  "data": {
    "task_id": "uuid",
    "level": "INFO|WARNING|ERROR|DEBUG",
    "component": "agent|tool|system",
    "message": "Python代码执行完成",
    "context": {
      "execution_time": 0.85,
      "output_length": 156
    }
  }
}
```

### 5. 任务状态消息 (task_status) - 增强版

```json
{
  "type": "task_status",
  "timestamp": "2025-01-17T10:31:00.000Z",
  "data": {
    "task_id": "uuid",
    "status": "running|completed|error|cancelled",
    "current_step": 3,
    "max_steps": 20,
    "message": "正在执行第3步",
    "progress_percentage": 15.0,
    "result": "任务结果（完成时）",
    "error": "错误信息（失败时）"
  }
}
```

### 6. 心跳消息 (heartbeat)

```json
{
  "type": "heartbeat",
  "timestamp": "2025-01-17T10:31:15.000Z",
  "data": {
    "task_id": "uuid",
    "status": "running",
    "current_step": 3,
    "message": "任务正在执行中...",
    "heartbeat_count": 5,
    "uptime": 45.2
  }
}
```

## 🔗 新增 API 端点

### 1. 获取任务详细执行信息

```http
GET /api/agent-details/tasks/{task_id}/execution
```

**响应示例**:

```json
{
  "task_id": "uuid",
  "agent_type": "manus",
  "instruction": "创建Python数组去重函数",
  "status": "completed",
  "current_step": 5,
  "max_steps": 20,
  "steps": [
    {
      "step": 1,
      "timestamp": "2025-01-17T10:30:00.000Z",
      "action": "分析需求",
      "status": "completed",
      "thinking": {
        "step": 1,
        "content": "分析用户需求...",
        "reasoning": "需要保持顺序..."
      },
      "tool_calls": [
        {
          "tool_name": "str_replace_editor",
          "tool_id": "uuid",
          "status": "success",
          "execution_time": 1.23
        }
      ],
      "duration": 5.67,
      "observations": ["需求分析完成"]
    }
  ],
  "start_time": "2025-01-17T10:30:00.000Z",
  "end_time": "2025-01-17T10:32:30.000Z",
  "total_duration": 150.5,
  "final_result": "数组去重函数创建完成"
}
```

### 2. 获取任务执行日志

```http
GET /api/agent-details/tasks/{task_id}/logs
```

### 3. 获取任务步骤汇总

```http
GET /api/agent-details/tasks/{task_id}/steps
```

**响应示例**:

```json
{
  "task_id": "uuid",
  "total_steps": 5,
  "steps": [
    {
      "step": 1,
      "action": "分析需求",
      "status": "completed",
      "timestamp": "2025-01-17T10:30:00.000Z",
      "duration": 5.67,
      "tool_calls_count": 1,
      "observations_count": 1,
      "has_error": false
    }
  ]
}
```

### 4. 获取工具调用统计

```http
GET /api/agent-details/tasks/{task_id}/tools
```

**响应示例**:

```json
{
  "task_id": "uuid",
  "total_tool_calls": 8,
  "tool_calls": [
    {
      "step": 1,
      "tool_name": "str_replace_editor",
      "tool_id": "uuid",
      "status": "success",
      "timestamp": "2025-01-17T10:30:00.000Z",
      "execution_time": 1.23,
      "has_error": false
    }
  ],
  "tool_statistics": {
    "str_replace_editor": {
      "count": 3,
      "success_count": 3,
      "error_count": 0,
      "total_time": 3.45
    },
    "python_execute": {
      "count": 5,
      "success_count": 4,
      "error_count": 1,
      "total_time": 12.67
    }
  }
}
```

### 5. 获取会话执行汇总

```http
GET /api/agent-details/sessions/{session_id}/summary
```

## 🔄 WebSocket 客户端示例

### JavaScript 客户端

```javascript
const ws = new WebSocket("ws://127.0.0.1:8000/ws/sessions/YOUR_SESSION_ID");

ws.onopen = function (event) {
  console.log("WebSocket连接已建立");

  // 订阅特定任务的事件
  ws.send(
    JSON.stringify({
      type: "subscribe_task",
      task_id: "YOUR_TASK_ID",
    })
  );
};

ws.onmessage = function (event) {
  const message = JSON.parse(event.data);
  console.log("收到消息:", message);

  switch (message.type) {
    case "thinking":
      console.log("Agent思考:", message.data.content);
      console.log("推理过程:", message.data.reasoning);
      break;

    case "tool_call":
      console.log("工具调用:", message.data.tool_name);
      console.log("调用状态:", message.data.status);
      break;

    case "step_update":
      console.log("步骤更新:", message.data.action);
      console.log("执行状态:", message.data.status);
      break;

    case "log":
      console.log(`[${message.data.level}] ${message.data.message}`);
      break;

    case "task_status":
      console.log("任务状态:", message.data.status);
      console.log("进度:", message.data.progress_percentage + "%");
      break;

    case "heartbeat":
      console.log("心跳:", message.data.heartbeat_count);
      break;
  }
};

// 获取任务详细信息
ws.send(
  JSON.stringify({
    type: "get_task_detail",
    task_id: "YOUR_TASK_ID",
  })
);
```

## 📊 使用场景示例

### 场景 1: 实时监控 Agent 执行过程

1. 创建会话和任务
2. 建立 WebSocket 连接
3. 订阅任务事件
4. 实时接收思考过程、工具调用、步骤更新
5. 获取最终执行结果和统计信息

### 场景 2: 调试 Agent 执行问题

1. 通过 API 获取任务详细执行信息
2. 查看每个步骤的执行状态和耗时
3. 分析工具调用的成功率和错误信息
4. 查看执行日志定位问题

### 场景 3: 性能分析和优化

1. 获取会话执行汇总
2. 分析工具调用统计
3. 查看步骤执行时间分布
4. 识别性能瓶颈

## 🔧 开发和测试

### 启动服务

```bash
cd web_api
python -m uvicorn main:app --reload --host 0.0.0.0 --port 8000
```

### 测试 WebSocket 连接

使用浏览器开发者工具或 WebSocket 测试工具连接到：
`ws://127.0.0.1:8000/ws/sessions/{session_id}`

### API 测试

访问 `http://127.0.0.1:8000/docs` 查看完整的 API 文档和测试界面。

## 📝 注意事项

1. **性能考虑**: 详细的监控会产生大量事件，建议在生产环境中适当调整推送频率
2. **存储限制**: 执行详情和日志会占用内存，建议定期清理过期数据
3. **WebSocket 连接**: 确保正确处理连接断开和重连逻辑
4. **错误处理**: 所有 API 都包含适当的错误处理和状态码

## 🎯 完整使用流程示例

### Python 客户端示例

```python
import asyncio
import websockets
import json
import requests

class AgentMonitor:
    def __init__(self, base_url="http://127.0.0.1:8000"):
        self.base_url = base_url
        self.session_id = None
        self.task_id = None

    async def create_session_and_task(self, instruction):
        """创建会话和任务"""
        # 创建会话
        response = requests.post(f"{self.base_url}/api/sessions")
        session_data = response.json()
        self.session_id = session_data["session_id"]
        print(f"创建会话: {self.session_id}")

        # 创建任务
        task_data = {"message": instruction}
        response = requests.post(
            f"{self.base_url}/api/sessions/{self.session_id}/tasks",
            json=task_data
        )
        task_info = response.json()
        self.task_id = task_info["task_id"]
        print(f"创建任务: {self.task_id}")

        return self.session_id, self.task_id

    async def monitor_execution(self):
        """监控任务执行过程"""
        uri = f"ws://127.0.0.1:8000/ws/sessions/{self.session_id}"

        async with websockets.connect(uri) as websocket:
            # 订阅任务事件
            await websocket.send(json.dumps({
                "type": "subscribe_task",
                "task_id": self.task_id
            }))

            print("开始监控任务执行...")

            async for message in websocket:
                data = json.loads(message)
                await self.handle_message(data)

                # 如果任务完成，退出监控
                if (data.get("type") == "task_status" and
                    data.get("data", {}).get("status") in ["completed", "error", "cancelled"]):
                    print("任务执行结束")
                    break

    async def handle_message(self, message):
        """处理WebSocket消息"""
        msg_type = message.get("type")
        data = message.get("data", {})
        timestamp = message.get("timestamp")

        if msg_type == "thinking":
            print(f"[{timestamp}] 🤔 思考: {data.get('content', '')[:100]}...")
            if data.get("reasoning"):
                print(f"    推理: {data.get('reasoning')}")

        elif msg_type == "tool_call":
            tool_name = data.get("tool_name")
            status = data.get("status")
            print(f"[{timestamp}] 🔧 工具调用: {tool_name} - {status}")
            if status == "success" and data.get("execution_time"):
                print(f"    执行时间: {data.get('execution_time'):.2f}秒")

        elif msg_type == "step_update":
            step = data.get("step")
            action = data.get("action")
            status = data.get("status")
            print(f"[{timestamp}] 📋 步骤{step}: {action} - {status}")
            if data.get("duration"):
                print(f"    耗时: {data.get('duration'):.2f}秒")

        elif msg_type == "log":
            level = data.get("level")
            component = data.get("component")
            message_text = data.get("message")
            print(f"[{timestamp}] 📝 [{level}] {component}: {message_text}")

        elif msg_type == "task_status":
            status = data.get("status")
            progress = data.get("progress_percentage", 0)
            print(f"[{timestamp}] 📊 任务状态: {status} ({progress:.1f}%)")

        elif msg_type == "heartbeat":
            count = data.get("heartbeat_count")
            print(f"[{timestamp}] 💓 心跳: {count}")

    async def get_execution_summary(self):
        """获取执行汇总信息"""
        if not self.task_id:
            return

        # 获取任务详细信息
        response = requests.get(
            f"{self.base_url}/api/agent-details/tasks/{self.task_id}/execution"
        )
        if response.status_code == 200:
            detail = response.json()
            if detail:
                print("\n📈 执行汇总:")
                print(f"  总步骤: {len(detail.get('steps', []))}")
                print(f"  总耗时: {detail.get('total_duration', 0):.2f}秒")
                print(f"  最终结果: {detail.get('final_result', 'N/A')[:100]}...")

        # 获取工具调用统计
        response = requests.get(
            f"{self.base_url}/api/agent-details/tasks/{self.task_id}/tools"
        )
        if response.status_code == 200:
            tools_info = response.json()
            print(f"\n🔧 工具调用统计:")
            print(f"  总调用次数: {tools_info.get('total_tool_calls', 0)}")

            stats = tools_info.get('tool_statistics', {})
            for tool_name, stat in stats.items():
                success_rate = (stat['success_count'] / stat['count'] * 100) if stat['count'] > 0 else 0
                print(f"  {tool_name}: {stat['count']}次调用, 成功率{success_rate:.1f}%")

# 使用示例
async def main():
    monitor = AgentMonitor()

    # 创建会话和任务
    instruction = "请帮我创建一个Python函数来实现数组去重，要求保持原有顺序"
    await monitor.create_session_and_task(instruction)

    # 监控执行过程
    await monitor.monitor_execution()

    # 获取执行汇总
    await monitor.get_execution_summary()

# 运行示例
# asyncio.run(main())
```

### cURL 测试命令

```bash
# 1. 创建会话
curl -X POST "http://127.0.0.1:8000/api/sessions"

# 2. 创建任务
curl -X POST "http://127.0.0.1:8000/api/sessions/{session_id}/tasks" \
  -H "Content-Type: application/json" \
  -d '{"message": "创建Python数组去重函数"}'

# 3. 获取任务详细执行信息
curl "http://127.0.0.1:8000/api/agent-details/tasks/{task_id}/execution"

# 4. 获取任务步骤汇总
curl "http://127.0.0.1:8000/api/agent-details/tasks/{task_id}/steps"

# 5. 获取工具调用统计
curl "http://127.0.0.1:8000/api/agent-details/tasks/{task_id}/tools"

# 6. 获取会话汇总
curl "http://127.0.0.1:8000/api/agent-details/sessions/{session_id}/summary"
```

## 🎯 后续扩展

- 支持 Agent 执行过程的录制和回放
- 添加执行性能指标和告警
- 支持多 Agent 协作的监控
- 集成更多的日志分析工具
- 添加执行过程的可视化界面
- 支持自定义事件过滤和订阅
