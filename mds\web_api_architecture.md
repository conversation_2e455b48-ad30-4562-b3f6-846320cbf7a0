# FastAPI Web API 集成方案

本文档旨在为 `open-manus` 二开项目提供一个 FastAPI Web API 的集成方案。该方案遵循一个核心原则：**在不修改任何现有项目文件的前提下，新增一个可独立部署和维护的 Web API 服务。**

## 方案概述

我们将在项目根目录下创建一个名为 `web_api` 的新目录，所有与 Web API 相关的代码都将存放在此目录中。这种方式可以确保新功能与现有核心业务逻辑的物理隔离，降低代码耦合度，方便未来独立进行维护和升级。

新的服务将通过调用 `app` 目录下现有的业务逻辑来执行任务，扮演一个对外暴露接口的“门面”角色。

## 建议的目录结构

```
RPA-AGENT-BASE-MANUS/
|-- web_api/
|   |-- __init__.py
|   |-- main.py             # FastAPI 应用入口
|   |-- routers/            # API 路由层
|   |   |-- __init__.py
|   |   |-- agent_router.py # 示例：Agent 相关路由
|   |   `-- task_router.py  # 示例：任务相关路由
|   |-- schemas/            # 数据模型层 (Pydantic Schemas)
|   |   |-- __init__.py
|   |   |-- agent.py        # Agent 相关数据模型
|   |   `-- task.py         # 任务相关数据模型
|   |-- services/           # 业务逻辑服务层
|   |   |-- __init__.py
|   |   |-- agent_service.py # 封装和调用核心 Agent 功能的服务
|   |   `-- task_service.py  # 封装任务管理逻辑的服务
|   |-- dependencies.py     # 依赖注入
|   `-- config.py           # API 服务相关配置
|
|-- app/
|   |-- ... (现有目录和文件，保持不变)
|-- ... (其他现有目录和文件，保持不变)
```

## 各模块职责说明

### 1. `web_api/main.py`

这是 FastAPI 应用的主入口文件。它的主要职责包括：

- 创建 `FastAPI` 实例。
- 挂载（include）`routers` 目录中定义的所有 API 路由。
- 定义一些全局的中间件（Middleware）或异常处理（Exception Handler）。

### 2. `web_api/routers/`

该目录用于存放 API 的路由（即端点，Endpoints）。通过将不同功能的路由拆分到不同的文件中，可以使代码结构更清晰。

- **`agent_router.py`**: 定义与 Agent 交互的 API，例如：`POST /agent/run_task` 用于创建一个新的代理任务。
- **`task_router.py`**: 定义与任务管理相关的 API，例如：`GET /tasks/{task_id}` 用于查询特定任务的状态。

### 3. `web_api/schemas/`

该目录存放 Pydantic 模型，用于 API 的数据校验、序列化和文档生成。

- **`task.py`**: 定义任务相关的数据结构，如 `TaskCreate`（创建任务时请求体的数据模型）和 `TaskInfo`（返回任务信息时响应体的数据模型）。

### 4. `web_api/services/`

这是业务逻辑的核心层。它作为 API 路由层和 `open-manus` 核心功能之间的桥梁。

- **职责**: 封装业务逻辑，处理来自 `routers` 层的请求，并调用 `app/` 目录下已有的功能模块来完成实际工作。
- **`agent_service.py`**: 例如，可以包含一个 `run_agent_task` 函数，该函数会 `import` 现有 `app` 模块中的 `ManusAgent` 类，并调用其方法来执行任务。通过这种方式，我们复用了现有代码，而没有修改它。

### 5. `web_api/dependencies.py`

用于定义 FastAPI 的依赖项。这些依赖项可以在多个路由中复用，例如：

- 获取数据库连接。
- 验证用户身份和权限。
- 从请求中提取通用参数。

### 6. `web_api/config.py`

存放与 Web API 服务本身相关的配置，例如服务监听的端口、CORS 设置、API 版本等。

## 请求处理流程示例

1.  客户端发送一个 `POST /agent/run_task` 请求，请求体中包含任务指令。
2.  FastAPI 应用接收到请求，匹配到 `web_api/routers/agent_router.py` 中定义的路由。
3.  `agent_router.py` 中的路径操作函数被调用。它会使用 `web_api/schemas/task.py` 中的 Pydantic 模型来验证请求体的数据格式。
4.  路径操作函数调用 `web_api/services/agent_service.py` 中的 `run_agent_task` 方法，并将经过验证的数据传递给它。
5.  `agent_service.py` 中的方法导入并使用 `app/agent/manus.py` 或其他核心模块的功能来执行实际的任务。
6.  `agent_service.py` 将执行结果返回给 `agent_router.py`。
7.  `agent_router.py` 将结果构造成符合 `schemas` 定义的响应模型，并返回给客户端。

## 如何运行

1.  **安装依赖**:

    ```bash
    pip install fastapi uvicorn
    ```

2.  **启动服务**:
    在项目根目录运行以下命令：
    ```bash
    uvicorn web_api.main:app --host 0.0.0.0 --port 8000 --reload
    ```
    - `--reload` 参数可以在代码变更后自动重启服务，方便开发。

## 总结

此方案通过创建一个独立的 `web_api` 模块，实现了在不侵入现有代码库的情况下，为项目增加 Web API 功能的目标。它结构清晰、易于扩展，并遵循了现代 Web 应用开发的最佳实践。
