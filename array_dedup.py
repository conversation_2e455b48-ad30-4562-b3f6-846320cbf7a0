def remove_duplicates(input_list):
    """
    Removes duplicate elements from a list while preserving the original order.
    
    Args:
        input_list: The list from which to remove duplicates.
        
    Returns:
        A new list with duplicate elements removed.
    """
    return list(dict.fromkeys(input_list))

# --- Example Usage ---

# Example 1: List of integers
numbers = [1, 2, 3, 2, 4, 5, 3, 6, 1]
unique_numbers = remove_duplicates(numbers)
print(f"Original list of numbers: {numbers}")
print(f"List after removing duplicates: {unique_numbers}")
print("-" * 20)

# Example 2: List of strings
fruits = ["apple", "banana", "cherry", "apple", "date", "banana"]
unique_fruits = remove_duplicates(fruits)
print(f"Original list of fruits: {fruits}")
print(f"List after removing duplicates: {unique_fruits}")
print("-" * 20)

# Note: Using set() is a more common and concise way, but it does not preserve order for Python versions before 3.7
# For modern Python (3.7+), set is also order-preserving.
# alternative_unique_numbers = list(set(numbers))
# print(f"Alternative method using set(): {alternative_unique_numbers}")
