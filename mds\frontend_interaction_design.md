# RPA 代理前端交互设计方案

## 📋 项目概述

基于 RPA 代理后端 Web API，设计一个现代化的前端交互界面，提供直观的任务管理、实时执行监控和工具调用可视化功能。

## 🎯 设计目标

1. **直观易用**: 类似 ChatGPT 的对话界面，降低学习成本
2. **实时反馈**: 实时显示任务执行进度和工具调用状态
3. **可视化**: 清晰展示工具调用链路和执行结果
4. **响应式**: 支持 PC 和移动端适配
5. **可扩展**: 支持自定义工具面板和配置

## 🎨 界面设计方案

### 整体布局 (Desktop)

```
┌─────────────────────────────────────────────────────────────┐
│ Header: RPA Agent Console                    [设置] [帮助] │
├─────────────────────────────────────────────────────────────┤
│ ┌─────────────┐ ┌─────────────────────────────────────────┐ │
│ │             │ │                                         │ │
│ │             │ │           主工作区域                     │ │
│ │   侧边栏     │ │                                         │ │
│ │             │ │                                         │ │
│ │             │ │                                         │ │
│ └─────────────┘ └─────────────────────────────────────────┘ │
├─────────────────────────────────────────────────────────────┤
│ Status Bar: 连接状态 | 当前任务 | 工具状态                    │
└─────────────────────────────────────────────────────────────┘
```

### 侧边栏 (Sidebar)

```
┌─────────────┐
│ 📋 会话管理  │
├─────────────┤
│ 🔧 工具面板  │
├─────────────┤
│ 📁 文件管理  │
├─────────────┤
│ 📊 执行历史  │
├─────────────┤
│ ⚙️ 设置     │
└─────────────┘
```

## 🖥️ 核心页面设计

### 1. 主对话界面

#### 布局结构

```
┌─────────────────────────────────────────────────────────────┐
│                    对话消息区域                              │
│ ┌─────────────────────────────────────────────────────────┐ │
│ │ 用户: 请帮我创建一个Python去重脚本               [时间]  │ │
│ └─────────────────────────────────────────────────────────┘ │
│ ┌─────────────────────────────────────────────────────────┐ │
│ │ 🤖 Agent: 好的，我来为您创建Python去重脚本        [时间]  │ │
│ │                                                         │ │
│ │ 📝 执行步骤:                                             │ │
│ │ ├─ Step 1: ✨ 思考中... [进行中]                        │ │
│ │ ├─ Step 2: 🔧 使用工具: str_replace_editor [已完成]     │ │
│ │ ├─ Step 3: 🔧 使用工具: python_execute [已完成]        │ │
│ │ └─ Step 4: 🏁 任务完成 [已完成]                         │ │
│ │                                                         │ │
│ │ 📊 执行结果:                                             │ │
│ │ ┌─────────────────────────────────────────────────────┐ │ │
│ │ │ 原始列表: [1, 2, 3, 2, 4, 5, 3, 6, 7, 7, 8]        │ │ │
│ │ │ 去重后的列表: [1, 2, 3, 4, 5, 6, 7, 8]              │ │ │
│ │ └─────────────────────────────────────────────────────┘ │ │
│ └─────────────────────────────────────────────────────────┘ │
├─────────────────────────────────────────────────────────────┤
│ 📝 [输入您的任务...]                            [发送] │ │
└─────────────────────────────────────────────────────────────┘
```

#### 消息类型设计

**1. 用户消息**

```typescript
interface UserMessage {
  id: string;
  type: "user";
  content: string;
  timestamp: Date;
  attachments?: File[];
}
```

**2. Agent 消息**

```typescript
interface AgentMessage {
  id: string;
  type: "agent";
  content: string;
  timestamp: Date;
  task?: TaskExecution;
  status: "thinking" | "executing" | "completed" | "error";
}
```

**3. 任务执行详情**

```typescript
interface TaskExecution {
  task_id: string;
  status: "running" | "completed" | "error";
  current_step: number;
  max_steps: number;
  steps: ExecutionStep[];
  result?: string;
  error?: string;
}

interface ExecutionStep {
  step: number;
  status: "pending" | "running" | "completed" | "error";
  action: string;
  thoughts?: string;
  tool_calls: ToolCall[];
  duration?: number;
}
```

### 2. 工具调用可视化

#### 工具调用卡片

```
┌─────────────────────────────────────────────────────────────┐
│ 🔧 str_replace_editor                            [已完成] │
├─────────────────────────────────────────────────────────────┤
│ 📥 输入参数:                                               │
│ {                                                          │
│   "command": "create",                                     │
│   "path": "G:\\RPA\\...\\deduplicate.py",                 │
│   "file_text": "# 创建一个包含重复元素的列表..."            │
│ }                                                          │
├─────────────────────────────────────────────────────────────┤
│ 📤 执行结果:                                               │
│ File created successfully at: G:\RPA\...\deduplicate.py   │
├─────────────────────────────────────────────────────────────┤
│ ⏱️ 执行时间: 0.05s                      📊 状态: 成功      │
└─────────────────────────────────────────────────────────────┘
```

#### 工具面板设计

```
┌─────────────────────────────────────────────────────────────┐
│                    🔧 可用工具                              │
├─────────────────────────────────────────────────────────────┤
│ 🖥️ 核心系统工具                                            │
│ ├─ bash                    执行命令行                      │
│ ├─ python_execute          执行Python代码                  │
│ ├─ str_replace_editor      文件编辑器                      │
│ └─ terminate               任务终止                        │
├─────────────────────────────────────────────────────────────┤
│ 🌐 浏览器网络工具                                          │
│ ├─ browser_use             浏览器自动化                    │
│ ├─ web_search              网络搜索                        │
│ └─ playwright              Playwright工具                  │
├─────────────────────────────────────────────────────────────┤
│ 📊 数据分析工具                                            │
│ ├─ data_visualization      数据可视化                      │
│ ├─ visualization_prepare   可视化准备                      │
│ └─ normal_python_execute   数据分析Python                  │
└─────────────────────────────────────────────────────────────┘
```

### 3. 会话管理界面

```
┌─────────────────────────────────────────────────────────────┐
│                    📋 会话管理                              │
├─────────────────────────────────────────────────────────────┤
│ [新建会话] [导入会话] [导出会话]                           │
├─────────────────────────────────────────────────────────────┤
│ 📝 会话-001                                    [活跃] │
│    创建时间: 2025-01-16 10:30:00                          │
│    任务数: 3 | 成功: 2 | 失败: 1                          │
│                                        [查看] [删除]      │
├─────────────────────────────────────────────────────────────┤
│ 📝 会话-002                                    [已完成] │
│    创建时间: 2025-01-16 09:15:00                          │
│    任务数: 1 | 成功: 1 | 失败: 0                          │
│                                        [查看] [删除]      │
└─────────────────────────────────────────────────────────────┘
```

## 🔄 实时交互流程

### 1. 建立连接流程

```mermaid
sequenceDiagram
    participant F as Frontend
    participant B as Backend

    F->>B: POST /api/sessions (创建会话)
    B->>F: session_id
    F->>B: WS /ws/sessions/{session_id} (建立WebSocket)
    B->>F: connection_established
    F->>F: 显示连接成功状态
```

### 2. 任务执行流程

```mermaid
sequenceDiagram
    participant U as User
    participant F as Frontend
    participant B as Backend

    U->>F: 输入任务描述
    F->>B: POST /api/sessions/{session_id}/tasks
    B->>F: task_id
    F->>F: 显示任务创建成功

    loop 实时状态更新
        B->>F: WS: task_status (任务状态)
        B->>F: WS: step_update (步骤更新)
        B->>F: WS: tool_call (工具调用)
        F->>F: 更新UI显示
    end

    B->>F: WS: task_completed
    F->>F: 显示最终结果
```

### 3. 错误处理流程

```mermaid
sequenceDiagram
    participant F as Frontend
    participant B as Backend

    B->>F: WS: error_event
    F->>F: 显示错误信息
    F->>U: 提供重试选项

    alt 用户选择重试
        F->>B: POST /api/sessions/{session_id}/tasks (重新创建任务)
    else 用户取消
        F->>B: POST /api/sessions/{session_id}/tasks/{task_id}/cancel
    end
```

## 💻 技术实现方案

### 1. 技术栈推荐

**基础框架**

- **React 18**: 主框架，支持并发特性
- **TypeScript**: 类型安全
- **Vite**: 构建工具，快速开发

**UI 组件库**

- **Ant Design**: 企业级 UI 组件
- **Tailwind CSS**: 快速样式开发
- **Framer Motion**: 动画效果

**状态管理**

- **Zustand**: 轻量级状态管理
- **React Query**: 服务端状态管理

**实时通信**

- **Socket.IO Client**: WebSocket 封装
- **EventSource**: SSE 支持

### 2. 核心组件设计

#### WebSocket 连接管理

```typescript
class WebSocketManager {
  private ws: WebSocket | null = null;
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 5;

  connect(sessionId: string): Promise<void>;
  disconnect(): void;
  send(message: any): void;
  onMessage(callback: (data: any) => void): void;
  onError(callback: (error: Error) => void): void;

  private reconnect(): void;
  private heartbeat(): void;
}
```

#### 任务状态管理

```typescript
interface TaskState {
  sessions: Map<string, Session>;
  currentSession: string | null;
  tasks: Map<string, Task>;
  messages: Message[];

  // Actions
  createSession(): Promise<string>;
  createTask(message: string): Promise<string>;
  updateTaskStatus(taskId: string, status: TaskStatus): void;
  addMessage(message: Message): void;
}
```

#### 工具调用组件

```typescript
interface ToolCallProps {
  toolCall: ToolCall;
  expanded?: boolean;
  onToggle?: () => void;
}

const ToolCallCard: React.FC<ToolCallProps> = ({
  toolCall,
  expanded,
  onToggle,
}) => {
  return (
    <Card>
      <ToolHeader tool={toolCall} status={toolCall.status} />
      {expanded && (
        <Collapse>
          <ToolInput args={toolCall.arguments} />
          <ToolOutput result={toolCall.result} />
          <ToolMetrics time={toolCall.execution_time} />
        </Collapse>
      )}
    </Card>
  );
};
```

### 3. 数据流设计

#### 状态结构

```typescript
interface AppState {
  // 连接状态
  connection: {
    status: "disconnected" | "connecting" | "connected" | "error";
    sessionId: string | null;
    lastError: string | null;
  };

  // 任务状态
  tasks: {
    current: Task | null;
    history: Task[];
    queue: string[];
  };

  // UI状态
  ui: {
    sidebarCollapsed: boolean;
    activeTab: "chat" | "tools" | "files" | "history";
    theme: "light" | "dark";
  };

  // 设置
  settings: {
    maxSteps: number;
    timeout: number;
    autoScroll: boolean;
    showToolDetails: boolean;
  };
}
```

#### 事件处理

```typescript
const useWebSocketEvents = (sessionId: string) => {
  const { updateTask, addMessage } = useTaskStore();

  useEffect(() => {
    const ws = new WebSocketManager();

    ws.onMessage((event) => {
      switch (event.type) {
        case "task_status":
          updateTask(event.data.task_id, event.data);
          break;
        case "tool_call":
          updateToolCall(event.data);
          break;
        case "log":
          addLogMessage(event.data);
          break;
      }
    });

    return () => ws.disconnect();
  }, [sessionId]);
};
```

## 🎨 用户体验设计

### 1. 加载状态

**思考状态动画**

```
🤖 正在思考... ⭕ → ⭕ → ⭕
```

**工具执行状态**

```
🔧 str_replace_editor [⏳ 执行中...]
🔧 str_replace_editor [✅ 已完成] (0.05s)
🔧 str_replace_editor [❌ 失败] (错误信息)
```

### 2. 交互反馈

**消息发送**

- 输入框禁用直到任务完成
- 显示"Agent 正在处理中..."
- 实时显示执行进度

**工具调用**

- 展开/折叠详细信息
- 参数和结果语法高亮
- 执行时间和状态指示

**错误处理**

- 友好的错误信息展示
- 重试按钮
- 错误日志查看

### 3. 响应式设计

**桌面端 (1200px+)**

- 侧边栏 + 主内容区域
- 工具面板悬浮显示
- 多列布局

**平板端 (768px - 1199px)**

- 可折叠侧边栏
- 单列布局
- 触摸友好的交互

**移动端 (< 768px)**

- 全屏对话界面
- 底部导航栏
- 手势操作支持

## 📱 移动端适配

### 界面布局

```
┌─────────────────────┐
│ 🤖 RPA Agent    ⚙️ │
├─────────────────────┤
│                     │
│    对话消息区域      │
│                     │
│                     │
├─────────────────────┤
│ [输入框]       [📎] │
├─────────────────────┤
│ 💬 🔧 📁 📊 ⚙️      │
└─────────────────────┘
```

### 移动端特性

- 上拉刷新会话列表
- 左滑删除会话
- 长按复制消息
- 语音输入支持

## 🔧 开发工具和配置

### 1. 项目结构

```
frontend/
├── src/
│   ├── components/        # 通用组件
│   │   ├── Chat/         # 对话相关组件
│   │   ├── Tools/        # 工具相关组件
│   │   ├── UI/           # 基础UI组件
│   │   └── Layout/       # 布局组件
│   ├── hooks/            # 自定义Hook
│   ├── services/         # API服务
│   ├── stores/           # 状态管理
│   ├── types/            # TypeScript类型
│   ├── utils/            # 工具函数
│   └── App.tsx
├── public/
├── package.json
└── vite.config.ts
```

### 2. 环境配置

```env
# API配置
VITE_API_BASE_URL=http://localhost:8000
VITE_WS_URL=ws://localhost:8000

# 功能开关
VITE_ENABLE_DEBUG=true
VITE_ENABLE_MOCK=false

# UI配置
VITE_THEME=light
VITE_LANGUAGE=zh-CN
```

### 3. 构建和部署

```json
{
  "scripts": {
    "dev": "vite",
    "build": "vite build",
    "preview": "vite preview",
    "test": "vitest",
    "lint": "eslint src --ext ts,tsx",
    "type-check": "tsc --noEmit"
  }
}
```

## 🎯 开发优先级

### Phase 1: 核心对话功能 (1-2 周)

- [ ] 基础对话界面
- [ ] WebSocket 连接管理
- [ ] 消息发送和接收
- [ ] 任务状态显示

### Phase 2: 工具调用可视化 (1 周)

- [ ] 工具调用卡片
- [ ] 执行步骤展示
- [ ] 参数和结果显示
- [ ] 错误处理 UI

### Phase 3: 会话管理 (1 周)

- [ ] 会话创建和切换
- [ ] 历史记录查看
- [ ] 会话导入导出
- [ ] 设置面板

### Phase 4: 优化和完善 (1 周)

- [ ] 响应式设计
- [ ] 性能优化
- [ ] 错误处理完善
- [ ] 用户体验优化

这个前端设计方案提供了完整的用户交互体验，与后端 API 无缝集成，支持实时监控和可视化展示 RPA 代理的执行过程。
