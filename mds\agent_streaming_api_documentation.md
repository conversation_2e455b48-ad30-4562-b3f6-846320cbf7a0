# Agent 实时流式传输前端接口文档

## 概述

本文档描述了 Agent 执行过程实时 Markdown 流式传输功能的前端接口规范。前端需要实现一个 Agent 执行监控页面，通过 WebSocket 实时接收 Agent 的执行过程并以 Markdown 格式渲染展示。

## 页面需求

### 页面功能

- Agent 执行过程实时监控
- Markdown 格式的执行日志展示
- WebSocket 连接管理
- 事件日志查看
- 历史记录查询

### 页面布局建议

- **左侧面板**: Markdown 渲染区域，显示格式化的 Agent 执行过程
- **右侧面板**: 实时事件日志，显示原始事件信息
- **顶部控制栏**: 连接控制、会话管理、清空按钮等
- **状态指示器**: WebSocket 连接状态显示

### 核心功能要求

1. **实时 Markdown 渲染**: 支持代码高亮、表格、列表等 Markdown 语法
2. **WebSocket 连接管理**: 连接、断开、重连、心跳检测
3. **事件日志**: 显示详细的事件信息和时间戳
4. **历史记录**: 支持查询和显示历史执行记录
5. **自动滚动**: 支持自动滚动到最新内容

## REST API 接口

### 1. 创建会话

**接口地址**: `POST /api/sessions`

**请求参数**:

```json
{
  "session_name": "string" // 会话名称
}
```

**响应参数**:

```json
{
  "session_id": "string", // 会话ID (UUID格式)
  "status": "active", // 会话状态
  "created_at": "string", // 创建时间 (ISO格式)
  "task_count": 0 // 任务数量
}
```

### 2. 获取会话列表

**接口地址**: `GET /api/sessions`

**请求参数**: 无

**响应参数**:

```json
{
  "sessions": [
    {
      "session_id": "string",
      "session_name": "string",
      "status": "active",
      "created_at": "string",
      "task_count": 0
    }
  ],
  "total": 0
}
```

### 3. 创建 Agent 任务

**接口地址**: `POST /api/sessions/{session_id}/tasks`

**路径参数**:

- `session_id`: 会话 ID

**请求参数**:

```json
{
  "message": "string" // 用户指令/任务描述
}
```

**响应参数**:

```json
{
  "task_id": "string", // 任务ID (UUID格式)
  "session_id": "string", // 会话ID
  "status": "pending", // 任务状态: pending/running/completed/error/cancelled
  "message": "string", // 任务描述
  "created_at": "string", // 创建时间
  "start_time": null, // 开始时间
  "end_time": null, // 结束时间
  "current_step": 0, // 当前步骤
  "result": null // 执行结果
}
```

### 4. 获取任务列表

**接口地址**: `GET /api/sessions/{session_id}/tasks`

**路径参数**:

- `session_id`: 会话 ID

**响应参数**:

```json
{
  "tasks": [
    {
      "task_id": "string",
      "status": "string",
      "message": "string",
      "created_at": "string",
      "start_time": "string",
      "end_time": "string",
      "current_step": 0,
      "result": "string"
    }
  ],
  "total": 0
}
```

### 5. 取消任务

**接口地址**: `POST /api/sessions/{session_id}/tasks/{task_id}/cancel`

**路径参数**:

- `session_id`: 会话 ID
- `task_id`: 任务 ID

**响应参数**:

```json
{
  "task_id": "string",
  "status": "cancelled",
  "message": "任务已取消"
}
```

## WebSocket 接口

### 连接地址

`ws://localhost:8000/ws/sessions/{session_id}`

### 连接流程

1. 确保会话存在（通过 REST API 创建）
2. 建立 WebSocket 连接
3. 接收连接确认消息
4. 开始接收实时事件

### 客户端发送消息格式

#### 1. 心跳检测

```json
{
  "type": "ping"
}
```

#### 2. 获取会话状态

```json
{
  "type": "get_status"
}
```

#### 3. 获取 Agent 执行历史

```json
{
  "type": "get_agent_history"
}
```

#### 4. 获取任务详情

```json
{
  "type": "get_task_detail",
  "task_id": "string"
}
```

#### 5. 订阅任务事件

```json
{
  "type": "subscribe_task",
  "task_id": "string"
}
```

### 服务端推送消息格式

#### 1. 连接确认

```json
{
  "type": "connection",
  "timestamp": "2025-01-21T14:30:00.000Z",
  "data": {
    "status": "connected",
    "session_id": "string",
    "message": "WebSocket 连接已建立"
  }
}
```

#### 2. Agent 事件推送 (核心消息)

```json
{
  "type": "agent_event",
  "timestamp": "2025-01-21T14:30:00.000Z",
  "data": {
    "event_type": "string", // 事件类型，见下方事件类型说明
    "step": 1, // 执行步骤
    "markdown": "string", // 格式化的Markdown内容
    "raw_event": {
      // 原始事件数据
      "event_type": "string",
      "timestamp": "string",
      "session_id": "string",
      "step": 1,
      "data": {} // 事件具体数据
    }
  }
}
```

#### 3. Agent 执行历史

```json
{
  "type": "agent_history",
  "timestamp": "2025-01-21T14:30:00.000Z",
  "data": {
    "session_id": "string",
    "markdown": "string", // 完整的Markdown执行日志
    "event_count": 15 // 事件总数
  }
}
```

#### 4. 会话状态

```json
{
  "type": "status",
  "timestamp": "2025-01-21T14:30:00.000Z",
  "data": {
    "session": {
      "session_id": "string",
      "status": "active",
      "created_at": "string"
    },
    "tasks": [],
    "task_count": 0
  }
}
```

#### 5. 心跳响应

```json
{
  "type": "pong",
  "timestamp": "2025-01-21T14:30:00.000Z",
  "data": {
    "message": "pong"
  }
}
```

#### 6. 错误消息

```json
{
  "type": "error",
  "timestamp": "2025-01-21T14:30:00.000Z",
  "data": {
    "error": "错误描述",
    "details": "详细错误信息"
  }
}
```

## Agent 事件类型说明

| 事件类型         | 描述           | Markdown 展示效果 |
| ---------------- | -------------- | ----------------- |
| `agent_start`    | Agent 开始执行 | ## 🚀 开始执行    |
| `agent_step`     | 执行步骤       | ### 📍 步骤 X/Y   |
| `agent_thinking` | 思考过程       | #### 💭 思考过程  |
| `tool_selection` | 工具选择       | #### 🛠️ 工具选择  |
| `tool_execution` | 工具执行       | #### ⚡ 执行工具  |
| `tool_result`    | 工具结果       | #### ✅ 工具结果  |
| `file_operation` | 文件操作       | #### 📁 文件操作  |
| `error`          | 错误信息       | #### ❌ 错误      |
| `agent_finish`   | 执行完成       | ## 🎉 执行完成    |

## 前端实现要点

### 1. WebSocket 连接管理

- 实现连接、断开、重连逻辑
- 定期发送心跳检测（建议 30 秒间隔）
- 处理连接异常和网络中断

### 2. Markdown 渲染

- 使用 Markdown 解析库（如 marked.js）
- 集成代码高亮库（如 Prism.js）
- 支持实时增量更新

### 3. 事件处理

- 监听`agent_event`消息进行实时更新
- 累积 Markdown 内容并渲染
- 记录事件日志用于调试

### 4. 用户体验

- 自动滚动到最新内容
- 支持手动滚动查看历史
- 提供清空、导出等功能
- 显示连接状态和加载指示器

### 5. 错误处理

- WebSocket 连接失败处理
- 消息解析错误处理
- 网络异常重连机制

## 测试建议

1. **连接测试**: 验证 WebSocket 连接建立和断开
2. **消息测试**: 测试各种消息类型的接收和处理
3. **渲染测试**: 验证 Markdown 内容正确渲染
4. **性能测试**: 测试大量事件的处理性能
5. **异常测试**: 测试网络中断、服务器重启等异常情况

## 注意事项

1. **会话管理**: 确保在连接 WebSocket 前会话已存在
2. **消息顺序**: Agent 事件按时间顺序推送，前端应保持顺序处理
3. **内存管理**: 长时间运行时注意清理历史数据避免内存泄漏
4. **跨域问题**: 如果前后端不同域，需要配置 CORS
5. **安全考虑**: 生产环境需要添加认证和授权机制
