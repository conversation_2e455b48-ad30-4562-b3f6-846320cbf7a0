# RPA 代理项目工具梳理文档

本文档详细梳理了 RPA 代理项目中的所有工具，按功能分类进行整理。

## 📁 工具目录结构

```
app/tool/
├── base.py                    # 工具基类定义
├── tool_collection.py        # 工具集合管理
├── __init__.py               # 工具模块导出
├──
├── 核心工具
├── bash.py                   # Bash命令执行
├── python_execute.py         # Python代码执行
├── str_replace_editor.py     # 文件编辑器
├── terminate.py              # 任务终止
├──
├── 浏览器与网络工具
├── browser_use_tool.py       # 浏览器自动化
├── playwright_tool.py        # Playwright工具代理
├── web_search.py            # 网络搜索
├──
├── 数据分析与可视化
├── chart_visualization/      # 图表可视化模块
│   ├── data_visualization.py # 数据可视化主工具
│   ├── chart_prepare.py     # 可视化准备工具
│   ├── python_execute.py    # 数据分析专用Python执行器
│   └── src/                 # TypeScript图表渲染代码
├──
├── 搜索引擎
├── search/                  # 搜索引擎实现
│   ├── base.py             # 搜索引擎基类
│   ├── google_search.py    # Google搜索
│   ├── baidu_search.py     # 百度搜索
│   ├── bing_search.py      # Bing搜索
│   └── duckduckgo_search.py # DuckDuckGo搜索
├──
├── 智能工具
├── planning.py             # 规划工具
├── create_chat_completion.py # 结构化对话生成
├── ask_human.py           # 人工干预工具
├──
├── 系统集成
├── mcp.py                 # MCP客户端工具
└── file_operators.py      # 文件操作接口
```

## 🔧 工具分类详解

### 1. 核心系统工具

#### 1.1 BaseTool (基础工具类)

- **文件**: `app/tool/base.py`
- **功能**: 所有工具的抽象基类
- **特性**:
  - 定义工具标准接口
  - 提供 ToolResult 结果封装
  - 支持参数验证和转换

#### 1.2 ToolCollection (工具集合)

- **文件**: `app/tool/tool_collection.py`
- **功能**: 管理多个工具的集合
- **特性**:
  - 工具注册和查找
  - 批量执行工具
  - 动态添加工具

#### 1.3 Bash (命令行执行)

- **文件**: `app/tool/bash.py`
- **功能**: 执行 bash 命令
- **特性**:
  - 交互式会话支持
  - 长运行命令处理
  - 超时控制
  - 后台任务支持

#### 1.4 PythonExecute (Python 代码执行)

- **文件**: `app/tool/python_execute.py`
- **功能**: 执行 Python 代码片段
- **特性**:
  - 沙箱式执行
  - 超时保护
  - 输出捕获
  - 多进程隔离

#### 1.5 StrReplaceEditor (文件编辑器)

- **文件**: `app/tool/str_replace_editor.py`
- **功能**: 文件查看、创建和编辑
- **特性**:
  - 字符串替换编辑
  - 文件插入操作
  - 撤销功能
  - 沙箱环境支持

#### 1.6 Terminate (任务终止)

- **文件**: `app/tool/terminate.py`
- **功能**: 终止当前任务执行
- **特性**:
  - 成功/失败状态标记
  - 清理资源

### 2. 浏览器与网络工具

#### 2.1 BrowserUseTool (浏览器自动化)

- **文件**: `app/tool/browser_use_tool.py`
- **功能**: 强大的浏览器自动化工具
- **主要操作**:
  - **导航**: 访问 URL、后退、刷新
  - **交互**: 点击元素、输入文本、下拉选择
  - **滚动**: 上下滚动、滚动到指定文本
  - **内容提取**: 基于目标的内容分析
  - **标签管理**: 切换、打开、关闭标签
  - **键盘操作**: 发送按键命令
- **特性**:
  - 状态保持
  - 元素索引定位
  - 集成 Web 搜索

#### 2.2 PlaywrightTool (Playwright 代理)

- **文件**: `app/tool/playwright_tool.py`
- **功能**: Playwright MCP 工具的代理
- **操作类型**:
  - launch, newPage, goto
  - fill, click, screenshot
  - evaluate, content
  - closePage, closeBrowser

#### 2.3 WebSearch (网络搜索)

- **文件**: `app/tool/web_search.py`
- **功能**: 多引擎网络搜索
- **特性**:
  - 多搜索引擎支持
  - 自动故障转移
  - 内容抓取选项
  - 语言和地区设置

### 3. 搜索引擎实现

#### 3.1 WebSearchEngine (搜索引擎基类)

- **文件**: `app/tool/search/base.py`
- **功能**: 搜索引擎的抽象基类

#### 3.2 具体搜索引擎

- **GoogleSearchEngine**: Google 搜索实现
- **BaiduSearchEngine**: 百度搜索实现
- **BingSearchEngine**: Bing 搜索实现
- **DuckDuckGoSearchEngine**: DuckDuckGo 搜索实现

### 4. 数据分析与可视化工具

#### 4.1 DataVisualization (数据可视化)

- **文件**: `app/tool/chart_visualization/data_visualization.py`
- **功能**: 基于 VMind 的图表可视化
- **输出格式**: PNG 图片、HTML 交互图表
- **支持语言**: 中文、英文
- **功能模式**:
  - 数据可视化
  - 图表洞察添加

#### 4.2 VisualizationPrepare (可视化准备)

- **文件**: `app/tool/chart_visualization/chart_prepare.py`
- **功能**: 数据可视化前置处理
- **工作模式**:
  - **Visualization 模式**: 数据 → 图表
    - 数据加载和清洗
    - CSV 数据生成
    - 图表描述生成
  - **Insight 模式**: 图表+洞察 → 增强图表
    - 选择数据洞察
    - 图表标注增强

#### 4.3 NormalPythonExecute (数据分析 Python 执行器)

- **文件**: `app/tool/chart_visualization/python_execute.py`
- **功能**: 专门用于数据分析的 Python 执行器
- **代码类型**:
  - process: 数据处理
  - report: 数据报告
  - others: 其他通用任务

### 5. 智能规划与交互工具

#### 5.1 PlanningTool (规划工具)

- **文件**: `app/tool/planning.py`
- **功能**: 创建和管理复杂任务计划
- **命令类型**:
  - create: 创建计划
  - update: 更新计划
  - list: 列出计划
  - get: 获取计划
  - set_active: 设置活跃计划
  - mark_step: 标记步骤状态
  - delete: 删除计划
- **步骤状态**:
  - not_started: 未开始
  - in_progress: 进行中
  - completed: 已完成
  - blocked: 被阻塞

#### 5.2 CreateChatCompletion (结构化对话生成)

- **文件**: `app/tool/create_chat_completion.py`
- **功能**: 生成结构化的对话完成
- **特性**:
  - 支持自定义响应类型
  - Pydantic 模型支持
  - JSON Schema 生成

#### 5.3 AskHuman (人工干预)

- **文件**: `app/tool/ask_human.py`
- **功能**: 请求人工帮助和干预
- **使用场景**: 需要人工决策或输入的情况

### 6. 系统集成工具

#### 6.1 MCPClients (MCP 客户端集合)

- **文件**: `app/tool/mcp.py`
- **功能**: Model Context Protocol 客户端工具集合
- **特性**:
  - 多服务器连接管理
  - SSE 传输支持
  - 工具名称清理
  - 会话管理

#### 6.2 MCPClientTool (MCP 客户端工具代理)

- **功能**: 单个 MCP 服务器工具的本地代理
- **特性**:
  - 远程工具调用
  - 错误处理
  - 结果转换

#### 6.3 FileOperator (文件操作接口)

- **文件**: `app/tool/file_operators.py`
- **功能**: 本地和沙箱环境的文件操作统一接口
- **操作类型**:
  - 文件读写
  - 目录检查
  - 文件存在性检查
  - 命令执行

## 🚀 工具使用特点

### 异步执行

- 所有工具均支持异步执行
- 提供超时控制和错误处理

### 参数验证

- 基于 JSON Schema 的参数验证
- 支持必需参数和可选参数

### 错误处理

- 统一的错误处理机制
- 详细的错误信息返回

### 状态管理

- 某些工具支持会话状态保持
- 如浏览器工具、Bash 工具等

### 扩展性

- 插件化架构
- 支持动态添加新工具
- MCP 协议支持外部工具集成

## 📊 工具统计

| 分类           | 工具数量  | 主要用途                       |
| -------------- | --------- | ------------------------------ |
| 核心系统工具   | 6 个      | 基础执行、文件操作、任务管理   |
| 浏览器网络工具 | 3 个      | 网页自动化、网络搜索           |
| 搜索引擎       | 4 个      | 多平台信息检索                 |
| 数据可视化     | 3 个      | 数据分析、图表生成             |
| 智能交互工具   | 3 个      | 任务规划、结构化输出、人工干预 |
| 系统集成工具   | 3 个      | 外部服务集成、文件系统操作     |
| **总计**       | **22 个** | **全方位 RPA 自动化能力**      |

这个工具生态系统提供了从基础系统操作到高级数据分析的完整 RPA 解决方案。
