"""
WebSocket 实时通信路由

提供会话的实时事件推送和Agent执行过程的Markdown流式传输
"""

import asyncio
import json
from datetime import datetime

from fastapi import APIRouter, HTTPException, WebSocket, WebSocketDisconnect

from app.logger import logger

from ..services.agent_event_emitter import AgentEvent, agent_event_emitter
from ..services.markdown_formatter import markdown_formatter
from ..services.state_manager import state_manager

router = APIRouter()


@router.websocket("/sessions/{session_id}")
async def websocket_endpoint(websocket: WebSocket, session_id: str):
    """WebSocket 端点，用于实时通信和Agent执行过程推送"""

    # 检查会话是否存在
    session = state_manager.get_session(session_id)
    if not session:
        # 使用标准的 403 Forbidden 状态码
        await websocket.close(code=1003, reason=f"会话 {session_id} 不存在")
        logger.warning(f"WebSocket 连接被拒绝: 会话 {session_id} 不存在")
        return

    # 接受 WebSocket 连接
    await websocket.accept()
    logger.info(f"WebSocket 连接建立: 会话 {session_id}")

    # 添加到状态管理器
    state_manager.add_websocket(session_id, websocket)

    # Agent事件监听器
    async def agent_event_listener(event: AgentEvent):
        """Agent事件监听器，将事件转换为Markdown并推送"""
        try:
            markdown_content = markdown_formatter.format_event(event)
            await websocket.send_json(
                {
                    "type": "agent_event",
                    "timestamp": datetime.now().isoformat(),
                    "data": {
                        "event_type": event.event_type.value,
                        "step": event.step,
                        "markdown": markdown_content,
                        "raw_event": event.to_dict(),
                    },
                }
            )
        except Exception as e:
            logger.error(f"发送Agent事件失败: {e}")

    # 注册事件监听器
    agent_event_emitter.add_listener(session_id, agent_event_listener)

    try:
        # 发送连接成功消息
        await websocket.send_json(
            {
                "type": "connection",
                "timestamp": datetime.now().isoformat(),
                "data": {
                    "status": "connected",
                    "session_id": session_id,
                    "message": "WebSocket 连接已建立",
                },
            }
        )

        # 保持连接活跃
        while True:
            try:
                # 接收客户端消息
                data = await websocket.receive_text()

                try:
                    message = json.loads(data)
                    logger.debug(f"收到 WebSocket 消息: {message}")

                    # 处理心跳消息
                    if message.get("type") == "ping":
                        await websocket.send_json(
                            {
                                "type": "pong",
                                "timestamp": datetime.now().isoformat(),
                                "data": {"message": "pong"},
                            }
                        )

                    # 处理其他类型的消息
                    elif message.get("type") == "get_status":
                        # 返回会话状态
                        session_info = state_manager.get_session(session_id)
                        tasks = state_manager.get_session_tasks(session_id)

                        await websocket.send_json(
                            {
                                "type": "status",
                                "timestamp": datetime.now().isoformat(),
                                "data": {
                                    "session": session_info,
                                    "tasks": tasks,
                                    "task_count": len(tasks),
                                },
                            }
                        )

                    # 处理获取Agent执行历史的请求
                    elif message.get("type") == "get_agent_history":
                        # 获取会话的所有Agent事件
                        events = agent_event_emitter.get_session_events(session_id)
                        markdown_content = markdown_formatter.format_events_stream(
                            events
                        )

                        await websocket.send_json(
                            {
                                "type": "agent_history",
                                "timestamp": datetime.now().isoformat(),
                                "data": {
                                    "session_id": session_id,
                                    "markdown": markdown_content,
                                    "event_count": len(events),
                                },
                            }
                        )

                    # 处理获取任务详细信息的请求
                    elif message.get("type") == "get_task_detail":
                        task_id = message.get("task_id")
                        if task_id:
                            # 这里可以从agent_adapter获取详细信息
                            # 目前返回基本任务信息
                            task_info = state_manager.get_task(task_id)
                            await websocket.send_json(
                                {
                                    "type": "task_detail",
                                    "timestamp": datetime.now().isoformat(),
                                    "data": {
                                        "task_id": task_id,
                                        "task_info": task_info,
                                    },
                                }
                            )
                        else:
                            await websocket.send_json(
                                {
                                    "type": "error",
                                    "timestamp": datetime.now().isoformat(),
                                    "data": {"error": "缺少task_id参数"},
                                }
                            )

                    # 处理订阅特定任务事件的请求
                    elif message.get("type") == "subscribe_task":
                        task_id = message.get("task_id")
                        if task_id:
                            await websocket.send_json(
                                {
                                    "type": "subscription_confirmed",
                                    "timestamp": datetime.now().isoformat(),
                                    "data": {
                                        "task_id": task_id,
                                        "message": f"已订阅任务 {task_id} 的事件推送",
                                    },
                                }
                            )
                        else:
                            await websocket.send_json(
                                {
                                    "type": "error",
                                    "timestamp": datetime.now().isoformat(),
                                    "data": {"error": "缺少task_id参数"},
                                }
                            )

                    else:
                        # 未知消息类型
                        await websocket.send_json(
                            {
                                "type": "error",
                                "timestamp": datetime.now().isoformat(),
                                "data": {
                                    "error": f"未知消息类型: {message.get('type')}",
                                    "received_message": message,
                                },
                            }
                        )

                except json.JSONDecodeError:
                    # JSON 解析错误
                    await websocket.send_json(
                        {
                            "type": "error",
                            "timestamp": datetime.now().isoformat(),
                            "data": {
                                "error": "无效的 JSON 格式",
                                "received_data": data,
                            },
                        }
                    )

            except WebSocketDisconnect:
                break
            except Exception as e:
                logger.error(f"WebSocket 消息处理错误: {str(e)}")
                await websocket.send_json(
                    {
                        "type": "error",
                        "timestamp": datetime.now().isoformat(),
                        "data": {"error": f"消息处理错误: {str(e)}"},
                    }
                )

    except WebSocketDisconnect:
        logger.info(f"WebSocket 连接断开: 会话 {session_id}")
    except Exception as e:
        logger.error(f"WebSocket 连接错误: {str(e)}")
    finally:
        # 清理连接和事件监听器
        state_manager.remove_websocket(session_id, websocket)
        agent_event_emitter.remove_listener(session_id, agent_event_listener)
        logger.info(f"WebSocket 连接清理完成: 会话 {session_id}")
