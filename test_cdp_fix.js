/**
 * 测试CDP修复的简单脚本
 */

const { spawn } = require('child_process');
const path = require('path');

async function testPlaywrightMCP() {
    console.log('🧪 测试playwright-mcp CDP修复...');
    
    const mcpPath = path.join(__dirname, 'external', 'playwright-mcp', 'cli.js');
    const configPath = path.join(__dirname, 'playwright-mcp-config.json');
    
    const args = [
        mcpPath,
        '--browser=chrome',
        '--no-sandbox',
        '--cdp-endpoint=auto',
        `--config=${configPath}`
    ];
    
    console.log(`启动命令: node ${args.join(' ')}`);
    
    const mcpProcess = spawn('node', args, {
        stdio: ['pipe', 'pipe', 'pipe']
    });
    
    let output = '';
    let errorOutput = '';
    
    mcpProcess.stdout.on('data', (data) => {
        const text = data.toString();
        output += text;
        console.log('📤 stdout:', text.trim());
    });
    
    mcpProcess.stderr.on('data', (data) => {
        const text = data.toString();
        errorOutput += text;
        console.log('📤 stderr:', text.trim());
    });
    
    // 等待5秒后发送测试命令
    setTimeout(() => {
        console.log('📝 发送测试命令...');
        const testCommand = {
            jsonrpc: "2.0",
            id: 1,
            method: "tools/call",
            params: {
                name: "browser_tab_new",
                arguments: {
                    url: "https://www.bing.com"
                }
            }
        };
        
        mcpProcess.stdin.write(JSON.stringify(testCommand) + '\n');
    }, 5000);
    
    // 10秒后结束测试
    setTimeout(() => {
        console.log('⏰ 测试时间结束，终止进程...');
        mcpProcess.kill('SIGTERM');
    }, 10000);
    
    mcpProcess.on('exit', (code, signal) => {
        console.log(`\n🏁 进程退出: code=${code}, signal=${signal}`);
        console.log('\n📊 测试结果:');
        
        if (errorOutput.includes('Invalid URL')) {
            console.log('❌ 仍然存在 "Invalid URL" 错误');
        } else if (errorOutput.includes('CDP端点解析成功')) {
            console.log('✅ CDP端点解析成功');
        } else {
            console.log('⚠️  未检测到明确的成功或失败信号');
        }
        
        console.log('\n📋 完整输出:');
        console.log('stdout:', output);
        console.log('stderr:', errorOutput);
    });
    
    mcpProcess.on('error', (error) => {
        console.error('❌ 进程启动失败:', error.message);
    });
}

testPlaywrightMCP().catch(console.error);
