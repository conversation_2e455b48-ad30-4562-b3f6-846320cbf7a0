"""
MCP服务管理API路由
"""
from typing import List
from fastapi import APIRouter, HTTPException, status
from fastapi.responses import JSONResponse

from web_api.schemas.mcp import (
    MCPServerConfigCreate,
    MCPServerConfigUpdate,
    MCPServerInfo,
    MCPServerListResponse,
    MCPConnectionRequest,
    MCPConnectionResponse,
    MCPOperationResponse,
    MCPServerStatusResponse,
    MCPServerStatus
)
from web_api.services.mcp_service import mcp_manager
from app.logger import logger

router = APIRouter(prefix="/api/mcp", tags=["MCP管理"])


@router.get("/servers", response_model=MCPServerListResponse, summary="获取MCP服务器列表")
async def list_servers():
    """
    获取所有MCP服务器的列表和状态信息
    """
    try:
        servers = await mcp_manager.list_servers()
        return MCPServerListResponse(
            servers=servers,
            total=len(servers)
        )
    except Exception as e:
        logger.error(f"Failed to list MCP servers: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取服务器列表失败: {str(e)}"
        )


@router.get("/servers/{server_id}", response_model=MCPServerInfo, summary="获取指定MCP服务器信息")
async def get_server(server_id: str):
    """
    获取指定MCP服务器的详细信息
    """
    try:
        server = await mcp_manager.get_server(server_id)
        if not server:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"服务器 {server_id} 不存在"
            )
        return server
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get MCP server {server_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取服务器信息失败: {str(e)}"
        )


@router.post("/servers", response_model=MCPServerInfo, summary="创建新的MCP服务器配置")
async def create_server(server_config: MCPServerConfigCreate):
    """
    创建新的MCP服务器配置
    """
    try:
        server = await mcp_manager.create_server(server_config)
        return server
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        logger.error(f"Failed to create MCP server: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"创建服务器配置失败: {str(e)}"
        )


@router.put("/servers/{server_id}", response_model=MCPServerInfo, summary="更新MCP服务器配置")
async def update_server(server_id: str, server_update: MCPServerConfigUpdate):
    """
    更新指定MCP服务器的配置
    """
    try:
        server = await mcp_manager.update_server(server_id, server_update)
        if not server:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"服务器 {server_id} 不存在"
            )
        return server
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to update MCP server {server_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"更新服务器配置失败: {str(e)}"
        )


@router.delete("/servers/{server_id}", response_model=MCPOperationResponse, summary="删除MCP服务器配置")
async def delete_server(server_id: str):
    """
    删除指定的MCP服务器配置
    """
    try:
        success = await mcp_manager.delete_server(server_id)
        if not success:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"服务器 {server_id} 不存在"
            )
        
        return MCPOperationResponse(
            success=True,
            message=f"服务器 {server_id} 已成功删除"
        )
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to delete MCP server {server_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"删除服务器配置失败: {str(e)}"
        )


@router.post("/servers/{server_id}/connect", response_model=MCPConnectionResponse, summary="连接到MCP服务器")
async def connect_server(server_id: str, request: MCPConnectionRequest = MCPConnectionRequest()):
    """
    连接到指定的MCP服务器
    """
    try:
        # 检查服务器是否存在
        server = await mcp_manager.get_server(server_id)
        if not server:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"服务器 {server_id} 不存在"
            )
        
        # 尝试连接
        success = await mcp_manager.connect_server(server_id, request.force)
        
        if success:
            # 获取更新后的服务器信息
            updated_server = await mcp_manager.get_server(server_id)
            return MCPConnectionResponse(
                success=True,
                message=f"成功连接到服务器 {server_id}",
                server_info=updated_server
            )
        else:
            # 获取错误信息
            status_info = await mcp_manager.get_server_status(server_id)
            error_msg = status_info.get("error_message", "连接失败")
            
            return MCPConnectionResponse(
                success=False,
                message=f"连接到服务器 {server_id} 失败: {error_msg}",
                server_info=server
            )
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to connect to MCP server {server_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"连接服务器失败: {str(e)}"
        )


@router.post("/servers/{server_id}/disconnect", response_model=MCPOperationResponse, summary="断开MCP服务器连接")
async def disconnect_server(server_id: str):
    """
    断开与指定MCP服务器的连接
    """
    try:
        # 检查服务器是否存在
        server = await mcp_manager.get_server(server_id)
        if not server:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"服务器 {server_id} 不存在"
            )
        
        # 尝试断开连接
        success = await mcp_manager.disconnect_server(server_id)
        
        if success:
            return MCPOperationResponse(
                success=True,
                message=f"成功断开与服务器 {server_id} 的连接"
            )
        else:
            # 获取错误信息
            status_info = await mcp_manager.get_server_status(server_id)
            error_msg = status_info.get("error_message", "断开连接失败")
            
            return MCPOperationResponse(
                success=False,
                message=f"断开与服务器 {server_id} 的连接失败: {error_msg}"
            )
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to disconnect from MCP server {server_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"断开连接失败: {str(e)}"
        )


@router.get("/servers/{server_id}/status", response_model=MCPServerStatusResponse, summary="获取MCP服务器状态")
async def get_server_status(server_id: str):
    """
    获取指定MCP服务器的详细状态信息
    """
    try:
        # 检查服务器是否存在
        server = await mcp_manager.get_server(server_id)
        if not server:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"服务器 {server_id} 不存在"
            )
        
        # 获取状态信息
        status_info = await mcp_manager.get_server_status(server_id)
        
        return MCPServerStatusResponse(
            server_id=server_id,
            status=MCPServerStatus(status_info["status"]),
            tools_count=status_info["tools_count"],
            error_message=status_info["error_message"],
            uptime=status_info["uptime"]
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get status for MCP server {server_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取服务器状态失败: {str(e)}"
        )
