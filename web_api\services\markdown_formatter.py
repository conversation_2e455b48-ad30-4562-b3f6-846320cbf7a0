"""
Markdown格式化器

将Agent事件转换为结构化的Markdown格式
"""

from datetime import datetime
from typing import Dict, Any, List
import json

from .agent_event_emitter import Agent<PERSON><PERSON>, AgentEventType


class MarkdownFormatter:
    """Markdown格式化器"""
    
    def __init__(self):
        self._formatters = {
            AgentEventType.AGENT_START: self._format_agent_start,
            AgentEventType.AGENT_STEP: self._format_agent_step,
            AgentEventType.AGENT_THINKING: self._format_agent_thinking,
            AgentEventType.TOOL_SELECTION: self._format_tool_selection,
            AgentEventType.TOOL_EXECUTION: self._format_tool_execution,
            AgentEventType.TOOL_RESULT: self._format_tool_result,
            AgentEventType.FILE_OPERATION: self._format_file_operation,
            AgentEventType.ERROR: self._format_error,
            AgentEventType.AGENT_FINISH: self._format_agent_finish,
        }
    
    def format_event(self, event: AgentEvent) -> str:
        """格式化单个事件为Markdown"""
        formatter = self._formatters.get(event.event_type)
        if formatter:
            return formatter(event)
        else:
            return self._format_unknown_event(event)
    
    def format_events_stream(self, events: List[AgentEvent]) -> str:
        """格式化事件流为完整的Markdown文档"""
        if not events:
            return "# Agent执行日志\n\n*暂无执行记录*\n"
        
        markdown_parts = [
            "# 🤖 Agent执行日志",
            "",
            f"**会话ID**: `{events[0].session_id}`",
            f"**开始时间**: {events[0].timestamp.strftime('%Y-%m-%d %H:%M:%S')}",
            f"**总步骤数**: {max(event.step for event in events)}",
            "",
            "---",
            ""
        ]
        
        for event in events:
            formatted = self.format_event(event)
            if formatted:
                markdown_parts.append(formatted)
                markdown_parts.append("")  # 添加空行分隔
        
        return "\n".join(markdown_parts)
    
    def _format_agent_start(self, event: AgentEvent) -> str:
        """格式化Agent开始事件"""
        prompt = event.data.get("prompt", "")
        return f"""## 🚀 开始执行

**时间**: {event.timestamp.strftime('%H:%M:%S')}

**用户请求**:
> {prompt}"""
    
    def _format_agent_step(self, event: AgentEvent) -> str:
        """格式化Agent步骤事件"""
        max_steps = event.data.get("max_steps", "未知")
        return f"""### 📍 步骤 {event.step}/{max_steps}

**时间**: {event.timestamp.strftime('%H:%M:%S')}"""
    
    def _format_agent_thinking(self, event: AgentEvent) -> str:
        """格式化Agent思考事件"""
        thoughts = event.data.get("thoughts", "")
        return f"""#### 💭 思考过程

```
{thoughts}
```"""
    
    def _format_tool_selection(self, event: AgentEvent) -> str:
        """格式化工具选择事件"""
        tools = event.data.get("tools", [])
        arguments = event.data.get("arguments", "")
        
        tools_list = ", ".join([f"`{tool}`" for tool in tools])
        
        markdown = f"""#### 🛠️ 工具选择

**选择的工具**: {tools_list}"""
        
        if arguments:
            try:
                # 尝试格式化JSON参数
                args_dict = json.loads(arguments)
                formatted_args = json.dumps(args_dict, indent=2, ensure_ascii=False)
                markdown += f"""

**参数**:
```json
{formatted_args}
```"""
            except:
                markdown += f"""

**参数**:
```
{arguments}
```"""
        
        return markdown
    
    def _format_tool_execution(self, event: AgentEvent) -> str:
        """格式化工具执行事件"""
        tool_name = event.data.get("tool_name", "未知工具")
        return f"""#### ⚡ 执行工具: `{tool_name}`

**时间**: {event.timestamp.strftime('%H:%M:%S')}"""
    
    def _format_tool_result(self, event: AgentEvent) -> str:
        """格式化工具结果事件"""
        tool_name = event.data.get("tool_name", "未知工具")
        result = event.data.get("result", "")
        
        # 限制结果长度，避免过长
        if len(result) > 1000:
            result = result[:1000] + "\n... (结果已截断)"
        
        return f"""#### ✅ 工具结果: `{tool_name}`

```
{result}
```"""
    
    def _format_file_operation(self, event: AgentEvent) -> str:
        """格式化文件操作事件"""
        operation = event.data.get("operation", "未知操作")
        file_path = event.data.get("file_path", "")
        content = event.data.get("content", "")
        
        markdown = f"""#### 📁 文件操作: {operation}

**文件路径**: `{file_path}`"""
        
        if content:
            # 根据文件扩展名确定代码块语言
            language = self._get_language_from_path(file_path)
            markdown += f"""

**内容**:
```{language}
{content}
```"""
        
        return markdown
    
    def _format_error(self, event: AgentEvent) -> str:
        """格式化错误事件"""
        error = event.data.get("error", "")
        return f"""#### ❌ 错误

**时间**: {event.timestamp.strftime('%H:%M:%S')}

```
{error}
```"""
    
    def _format_agent_finish(self, event: AgentEvent) -> str:
        """格式化Agent完成事件"""
        result = event.data.get("result", "")
        return f"""## 🎉 执行完成

**时间**: {event.timestamp.strftime('%H:%M:%S')}

**结果**:
```
{result}
```"""
    
    def _format_unknown_event(self, event: AgentEvent) -> str:
        """格式化未知事件"""
        return f"""#### ❓ 未知事件: {event.event_type.value}

**时间**: {event.timestamp.strftime('%H:%M:%S')}

**数据**:
```json
{json.dumps(event.data, indent=2, ensure_ascii=False)}
```"""
    
    def _get_language_from_path(self, file_path: str) -> str:
        """根据文件路径获取代码块语言"""
        if not file_path:
            return ""
        
        extension_map = {
            ".py": "python",
            ".js": "javascript",
            ".ts": "typescript",
            ".html": "html",
            ".css": "css",
            ".json": "json",
            ".yaml": "yaml",
            ".yml": "yaml",
            ".xml": "xml",
            ".md": "markdown",
            ".sh": "bash",
            ".sql": "sql",
            ".java": "java",
            ".cpp": "cpp",
            ".c": "c",
            ".go": "go",
            ".rs": "rust",
            ".php": "php",
            ".rb": "ruby",
        }
        
        for ext, lang in extension_map.items():
            if file_path.lower().endswith(ext):
                return lang
        
        return ""


# 全局格式化器实例
markdown_formatter = MarkdownFormatter()
