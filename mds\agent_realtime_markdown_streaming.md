# Agent实时Markdown流式传输实现

## 概述

本文档记录了Agent执行过程实时Markdown流式传输功能的完整实现。该功能能够将Agent的思考、工具调用、文件操作等执行过程实时转换为结构化的Markdown格式，并通过WebSocket流式传输到前端进行渲染。

## 实现架构

### 核心组件

1. **AgentEventEmitter** (`web_api/services/agent_event_emitter.py`)
   - 事件发射器，负责捕获Agent执行过程中的关键事件
   - 支持多种事件类型：开始、步骤、思考、工具选择、工具执行、文件操作、错误、完成
   - 提供事件监听器管理和事件历史存储

2. **MarkdownFormatter** (`web_api/services/markdown_formatter.py`)
   - Markdown格式化器，将Agent事件转换为结构化的Markdown
   - 支持代码高亮、文件操作展示、错误信息格式化
   - 提供单个事件和事件流的格式化功能

3. **AgentStreamAdapter** (`web_api/services/agent_stream_adapter.py`)
   - Agent流式适配器，通过方法拦截捕获Agent执行过程
   - 无侵入式设计，不修改原有Agent代码
   - 支持方法恢复，确保资源清理

4. **WebSocket扩展** (`web_api/routers/websocket.py`)
   - 扩展现有WebSocket路由，支持Agent事件推送
   - 提供实时事件传输和历史记录查询
   - 支持多客户端连接和会话隔离

### 事件类型

| 事件类型 | 描述 | Markdown格式 |
|---------|------|-------------|
| `agent_start` | Agent开始执行 | 🚀 开始执行 |
| `agent_step` | 执行步骤 | 📍 步骤 X/Y |
| `agent_thinking` | 思考过程 | 💭 思考过程 |
| `tool_selection` | 工具选择 | 🛠️ 工具选择 |
| `tool_execution` | 工具执行 | ⚡ 执行工具 |
| `tool_result` | 工具结果 | ✅ 工具结果 |
| `file_operation` | 文件操作 | 📁 文件操作 |
| `error` | 错误信息 | ❌ 错误 |
| `agent_finish` | 执行完成 | 🎉 执行完成 |

## 使用方法

### 1. 启动Web API服务器

```bash
# 激活虚拟环境
.venv\Scripts\activate

# 启动服务器
python run_web_api.py
```

### 2. 创建会话

```bash
curl -X POST "http://localhost:8000/api/sessions" \
  -H "Content-Type: application/json" \
  -d '{"session_name": "测试会话"}'
```

### 3. 连接WebSocket

```javascript
const ws = new WebSocket('ws://localhost:8000/ws/sessions/your-session-id');

ws.onmessage = (event) => {
    const data = JSON.parse(event.data);
    if (data.type === 'agent_event') {
        const markdown = data.data.markdown;
        // 渲染Markdown内容
        renderMarkdown(markdown);
    }
};
```

### 4. 执行Agent任务

```bash
curl -X POST "http://localhost:8000/api/sessions/your-session-id/tasks" \
  -H "Content-Type: application/json" \
  -d '{"message": "帮我写一个Python数组去重函数"}'
```

## 测试工具

### 1. 命令行测试

```bash
# 完整测试
python test_agent_stream.py

# 只测试格式化器
python test_agent_stream.py --test-formatter
```

### 2. 前端测试页面

打开 `web_api/static/agent_stream_test.html` 进行可视化测试：

- 实时Markdown渲染
- 事件日志查看
- WebSocket连接管理
- 历史记录查询

## 技术特点

### 1. 无侵入式设计

- 通过方法拦截实现事件捕获
- 不修改原有Agent代码
- 支持动态启用/禁用

### 2. 实时流式传输

- WebSocket实时推送
- 增量Markdown更新
- 低延迟事件传输

### 3. 结构化Markdown

- 语法高亮支持
- 文件操作展示
- 错误信息格式化
- 响应式设计

### 4. 多会话支持

- 会话隔离
- 并发执行
- 资源管理

## WebSocket消息格式

### Agent事件消息

```json
{
    "type": "agent_event",
    "timestamp": "2025-01-21T14:30:00.000Z",
    "data": {
        "event_type": "agent_thinking",
        "step": 1,
        "markdown": "#### 💭 思考过程\n\n```\n我需要分析这个问题...\n```",
        "raw_event": {
            "event_type": "agent_thinking",
            "timestamp": "2025-01-21T14:30:00.000Z",
            "session_id": "session-123",
            "step": 1,
            "data": {
                "thoughts": "我需要分析这个问题..."
            }
        }
    }
}
```

### 历史记录消息

```json
{
    "type": "agent_history",
    "timestamp": "2025-01-21T14:30:00.000Z",
    "data": {
        "session_id": "session-123",
        "markdown": "# 🤖 Agent执行日志\n\n...",
        "event_count": 15
    }
}
```

## 文件结构

```
web_api/
├── services/
│   ├── agent_event_emitter.py    # 事件发射器
│   ├── markdown_formatter.py     # Markdown格式化器
│   ├── agent_stream_adapter.py   # 流式适配器
│   └── agent_adapter.py          # Agent适配器(已修改)
├── routers/
│   └── websocket.py              # WebSocket路由(已扩展)
└── static/
    └── agent_stream_test.html     # 测试页面

test_agent_stream.py              # 测试脚本
```

## 性能优化

### 1. 事件缓存

- 会话级事件存储
- 内存使用控制
- 自动清理机制

### 2. Markdown优化

- 增量渲染
- 内容截断
- 代码高亮缓存

### 3. WebSocket优化

- 连接池管理
- 心跳检测
- 自动重连

## 扩展功能

### 1. 事件过滤

可以根据事件类型进行过滤：

```python
# 只监听思考和工具事件
filtered_types = [AgentEventType.AGENT_THINKING, AgentEventType.TOOL_EXECUTION]
```

### 2. 自定义格式化

可以扩展MarkdownFormatter添加自定义格式：

```python
def _format_custom_event(self, event: AgentEvent) -> str:
    # 自定义格式化逻辑
    return f"### 自定义事件\n{event.data}"
```

### 3. 多客户端同步

支持多个客户端同时观看同一会话的执行过程。

## 故障排除

### 1. WebSocket连接失败

- 检查服务器是否启动
- 确认会话ID是否存在
- 检查防火墙设置

### 2. 事件丢失

- 检查事件监听器是否正确注册
- 确认Agent适配器是否正确初始化
- 查看服务器日志

### 3. Markdown渲染异常

- 检查marked.js是否正确加载
- 确认Prism.js代码高亮是否工作
- 验证Markdown语法是否正确

## 总结

该实现提供了一个完整的Agent执行过程实时可视化解决方案，具有以下优势：

1. **实时性**: 通过WebSocket实现毫秒级事件推送
2. **可视化**: 结构化Markdown展示，支持代码高亮
3. **无侵入**: 不修改原有Agent代码，通过适配器实现
4. **可扩展**: 模块化设计，易于扩展新功能
5. **易用性**: 提供完整的测试工具和示例

这个方案可以帮助开发者更好地理解和调试Agent的执行过程，提升开发效率和用户体验。
