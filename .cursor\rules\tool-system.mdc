# Tool System Guide

## Tool Architecture

- **Base Tool**: [app/tool/base.py](mdc:app/tool/base.py) - Abstract base class for all tools
- **Tool Collection**: [app/tool/tool_collection.py](mdc:app/tool/tool_collection.py) - Manages tool registration and access

## Available Tools

### Core Tools

- **StrReplaceEditor**: [app/tool/str_replace_editor.py](mdc:app/tool/str_replace_editor.py) - File editing with search/replace
- **PythonExecute**: [app/tool/python_execute.py](mdc:app/tool/python_execute.py) - Python code execution
- **BashTool**: [app/tool/bash.py](mdc:app/tool/bash.py) - Shell command execution
- **FileOperators**: [app/tool/file_operators.py](mdc:app/tool/file_operators.py) - File system operations

### Browser & Web Tools

- **BrowserUseTool**: [app/tool/browser_use_tool.py](mdc:app/tool/browser_use_tool.py) - Advanced browser automation
- **PlaywrightTool**: [app/tool/playwright_tool.py](mdc:app/tool/playwright_tool.py) - Playwright integration
- **WebSearch**: [app/tool/web_search.py](mdc:app/tool/web_search.py) - Web search capabilities

### Communication & Analysis Tools

- **AskHuman**: [app/tool/ask_human.py](mdc:app/tool/ask_human.py) - Human interaction
- **PlanningTool**: [app/tool/planning.py](mdc:app/tool/planning.py) - Task planning and organization
- **CreateChatCompletion**: [app/tool/create_chat_completion.py](mdc:app/tool/create_chat_completion.py) - LLM interaction

### MCP Tools

- **MCPClients**: [app/tool/mcp.py](mdc:app/tool/mcp.py) - Model Context Protocol client
- **MCPClientTool**: Integration for MCP-based tools

### Utility Tools

- **Terminate**: [app/tool/terminate.py](mdc:app/tool/terminate.py) - Task termination
- **ChartVisualization**: [app/tool/chart_visualization/](mdc:app/tool/chart_visualization/) - Data visualization

## Tool Implementation Patterns

### Base Tool Structure

```python
from app.tool.base import BaseTool

class MyTool(BaseTool):
    name: str = "my_tool"
    description: str = "Description of what the tool does"

    async def execute(self, **kwargs) -> str:
        # Tool implementation
        return result
```

### Tool Registration

Tools are registered in `ToolCollection`:

```python
available_tools: ToolCollection = Field(
    default_factory=lambda: ToolCollection(
        MyTool(),
        AnotherTool(),
    )
)
```

## MCP Integration

Model Context Protocol tools extend functionality:

- Connect to external MCP servers
- Dynamic tool discovery
- Standardized tool interface

## Best Practices

- Inherit from `BaseTool` for consistency
- Use descriptive names and documentation
- Implement proper error handling
- Follow async patterns for I/O operations
- Use type hints for parameters and returns
- Register tools through `ToolCollection`
  description:
  globs:
  alwaysApply: false

---
