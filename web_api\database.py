"""
数据库连接管理模块
"""
from sqlalchemy import create_engine
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker, Session
from .config import config

# 创建数据库引擎
engine = create_engine(
    config.database_url,
    pool_pre_ping=True,  # 自动检测连接是否有效
    pool_recycle=3600,   # 一小时后回收连接
)

# 创建会话工厂
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

# 创建基本模型类
Base = declarative_base()

def get_db() -> Session:
    """获取数据库会话"""
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()

# 导入模型以确保表创建
from . import models
