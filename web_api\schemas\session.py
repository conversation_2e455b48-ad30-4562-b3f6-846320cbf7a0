"""
会话相关的数据模型
"""

from typing import List, Optional
from datetime import datetime
from pydantic import BaseModel, Field


class SessionCreate(BaseModel):
    """创建会话请求模型"""
    pass  # 简化版无需额外参数


class SessionResponse(BaseModel):
    """会话响应模型"""
    session_id: str = Field(..., description="会话ID")
    status: str = Field(..., description="会话状态")
    created_at: datetime = Field(..., description="创建时间")
    task_count: int = Field(default=0, description="任务数量")


class SessionList(BaseModel):
    """会话列表响应模型"""
    sessions: List[SessionResponse] = Field(..., description="会话列表")
    total: int = Field(..., description="会话总数")
