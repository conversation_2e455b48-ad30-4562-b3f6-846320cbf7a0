#!/usr/bin/env python3
"""
Agent流式传输测试脚本

用于测试Agent执行过程的实时Markdown流式传输功能
"""

import asyncio
import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from app.agent.manus import Manus
from web_api.services.agent_event_emitter import agent_event_emitter
from web_api.services.agent_stream_adapter import create_streaming_agent
from web_api.services.markdown_formatter import markdown_formatter
from app.logger import logger


class StreamTestCollector:
    """流式测试收集器，用于收集和显示Agent执行过程"""
    
    def __init__(self, session_id: str):
        self.session_id = session_id
        self.events = []
        self.markdown_parts = []
    
    async def event_handler(self, event):
        """事件处理器"""
        self.events.append(event)
        
        # 格式化为Markdown
        markdown = markdown_formatter.format_event(event)
        self.markdown_parts.append(markdown)
        
        # 实时打印事件
        print(f"\n{'='*60}")
        print(f"🎯 事件类型: {event.event_type.value}")
        print(f"⏰ 时间: {event.timestamp.strftime('%H:%M:%S')}")
        print(f"📍 步骤: {event.step}")
        print(f"📝 Markdown:")
        print(markdown)
        print(f"{'='*60}")
    
    def get_full_markdown(self) -> str:
        """获取完整的Markdown文档"""
        return markdown_formatter.format_events_stream(self.events)


async def test_agent_streaming():
    """测试Agent流式传输功能"""
    session_id = "test-session-streaming"
    
    print("🚀 开始测试Agent流式传输功能")
    print(f"📋 会话ID: {session_id}")
    print("-" * 60)
    
    # 创建事件收集器
    collector = StreamTestCollector(session_id)
    
    # 注册事件监听器
    agent_event_emitter.add_listener(session_id, collector.event_handler)
    
    try:
        # 创建Agent实例
        print("🤖 创建Agent实例...")
        agent = await Manus.create()
        
        # 创建流式适配器
        print("🔧 创建流式适配器...")
        stream_adapter = create_streaming_agent(agent, session_id)
        
        # 测试提示
        test_prompt = "帮我创建一个Python函数，用于计算斐波那契数列的前n项"
        
        print(f"💭 测试提示: {test_prompt}")
        print("⚡ 开始执行...")
        print("-" * 60)
        
        # 执行Agent任务
        result = await agent.run(test_prompt)
        
        print("\n" + "="*60)
        print("✅ Agent执行完成!")
        print(f"📊 结果: {result}")
        print(f"📈 总共收集到 {len(collector.events)} 个事件")
        
        # 生成完整的Markdown报告
        print("\n" + "="*60)
        print("📄 生成完整Markdown报告...")
        full_markdown = collector.get_full_markdown()
        
        # 保存到文件
        report_file = project_root / "test_agent_stream_report.md"
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write(full_markdown)
        
        print(f"💾 报告已保存到: {report_file}")
        
        # 显示部分报告内容
        print("\n" + "="*60)
        print("📋 Markdown报告预览:")
        print("-" * 60)
        preview = full_markdown[:1000] + "..." if len(full_markdown) > 1000 else full_markdown
        print(preview)
        
        # 恢复原始方法
        stream_adapter.restore_methods()
        
        # 清理Agent
        await agent.cleanup()
        
        print("\n" + "="*60)
        print("🎉 测试完成!")
        print(f"📊 事件统计:")
        event_types = {}
        for event in collector.events:
            event_type = event.event_type.value
            event_types[event_type] = event_types.get(event_type, 0) + 1
        
        for event_type, count in event_types.items():
            print(f"  - {event_type}: {count} 次")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        logger.error(f"Agent流式传输测试失败: {str(e)}", exc_info=True)
        return False
        
    finally:
        # 清理事件监听器
        agent_event_emitter.remove_listener(session_id, collector.event_handler)
        agent_event_emitter.clear_session_events(session_id)


async def test_markdown_formatter():
    """测试Markdown格式化器"""
    print("\n🧪 测试Markdown格式化器...")
    
    from web_api.services.agent_event_emitter import AgentEvent, AgentEventType
    from datetime import datetime
    
    # 创建测试事件
    test_events = [
        AgentEvent(
            event_type=AgentEventType.AGENT_START,
            timestamp=datetime.now(),
            session_id="test",
            step=0,
            data={"prompt": "测试提示"}
        ),
        AgentEvent(
            event_type=AgentEventType.AGENT_THINKING,
            timestamp=datetime.now(),
            session_id="test",
            step=1,
            data={"thoughts": "我需要分析这个问题..."}
        ),
        AgentEvent(
            event_type=AgentEventType.TOOL_SELECTION,
            timestamp=datetime.now(),
            session_id="test",
            step=1,
            data={"tools": ["python_execute"], "arguments": '{"code": "print(\\"Hello\\")"}'}
        )
    ]
    
    # 测试单个事件格式化
    for event in test_events:
        markdown = markdown_formatter.format_event(event)
        print(f"\n📝 {event.event_type.value} 格式化结果:")
        print(markdown)
    
    # 测试事件流格式化
    full_markdown = markdown_formatter.format_events_stream(test_events)
    print(f"\n📄 完整事件流格式化结果:")
    print(full_markdown[:500] + "..." if len(full_markdown) > 500 else full_markdown)


def print_usage():
    """打印使用说明"""
    print("""
🤖 Agent流式传输测试工具

用法:
    python test_agent_stream.py [选项]

选项:
    --test-formatter    只测试Markdown格式化器
    --help             显示此帮助信息

示例:
    python test_agent_stream.py                # 完整测试
    python test_agent_stream.py --test-formatter  # 只测试格式化器
    """)


async def main():
    """主函数"""
    args = sys.argv[1:]
    
    if "--help" in args:
        print_usage()
        return
    
    if "--test-formatter" in args:
        await test_markdown_formatter()
        return
    
    # 完整测试
    print("🧪 Agent流式传输完整测试")
    print("=" * 60)
    
    # 测试格式化器
    await test_markdown_formatter()
    
    # 测试流式传输
    success = await test_agent_streaming()
    
    if success:
        print("\n✅ 所有测试通过!")
        print("\n💡 接下来可以:")
        print("1. 启动Web API服务器: python run_web_api.py")
        print("2. 打开测试页面: web_api/static/agent_stream_test.html")
        print("3. 创建会话并测试实时流式传输")
    else:
        print("\n❌ 测试失败，请检查错误信息")
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())
