# Terminate 工具 Cancel Scope 错误修复

## 问题描述

在使用 `run_web_api.py` 启动 Web API 服务时，每次调用 terminate 工具完成任务后，日志都会报告以下错误：

```
ERROR:    Traceback (most recent call last):
  File "C:\Python311\Lib\contextlib.py", line 222, in __aexit__
    await self.gen.athrow(typ, value, traceback)
  ...
  asyncio.exceptions.CancelledError: Cancelled by cancel scope 1dcb292dc50

2025-07-21 17:40:17.086 | WARNING  | app.tool.mcp:disconnect:175 - Cancel scope error during disconnect from playwright, continuing with cleanup: Attempted to exit cancel scope in a different task than it was entered in
```

虽然这个错误不影响功能，但会产生大量错误日志，影响系统的可维护性。

## 问题分析

### 根本原因

1. **AsyncExitStack 的 cancel scope 问题**：
   - MCP 客户端使用 `AsyncExitStack` 管理异步上下文
   - 在断开连接时，`AsyncExitStack.aclose()` 被调用
   - 由于异步任务的生命周期问题，cancel scope 在不同的任务中被进入和退出

2. **FastAPI 生命周期管理冲突**：
   - terminate 工具触发 Manus 实例清理
   - 清理过程与 FastAPI 的生命周期管理产生冲突
   - 多层嵌套的异步上下文管理器导致 cancel scope 错误

3. **异步清理顺序问题**：
   - 多个异步清理任务同时执行
   - 缺乏适当的错误处理和超时机制

## 修复方案

### 1. MCP 客户端断开连接优化

**文件**: `app/tool/mcp.py`

**主要改进**:
- 使用 `asyncio.shield()` 保护清理过程不被取消
- 增强异常处理，特别是 `CancelledError` 和 cancel scope 错误
- 实现并行安全断开连接，使用 `asyncio.gather()` 处理多个服务器
- 添加 `_safe_disconnect_single()` 方法确保单个服务器断开的安全性

```python
async def disconnect(self, server_id: str = "") -> None:
    """Disconnect from a specific MCP server or all servers if no server_id provided."""
    if server_id:
        if server_id in self.sessions:
            try:
                exit_stack = self.exit_stacks.get(server_id)
                if exit_stack:
                    try:
                        # 使用 asyncio.shield 保护清理过程不被取消
                        await asyncio.shield(exit_stack.aclose())
                    except (RuntimeError, asyncio.CancelledError) as e:
                        if "cancel scope" in str(e).lower() or isinstance(e, asyncio.CancelledError):
                            logger.warning(f"Cancel scope error during disconnect from {server_id}, continuing with cleanup: {e}")
                        else:
                            raise
                    except Exception as e:
                        logger.warning(f"Unexpected error during exit stack cleanup for {server_id}: {e}")
                # ... 清理引用和工具映射
            except Exception as e:
                logger.error(f"Error disconnecting from server {server_id}: {e}")
    else:
        # 并行断开所有服务器连接
        disconnect_tasks = []
        for sid in sorted(list(self.sessions.keys())):
            task = asyncio.create_task(self._safe_disconnect_single(sid))
            disconnect_tasks.append(task)
        
        if disconnect_tasks:
            await asyncio.gather(*disconnect_tasks, return_exceptions=True)
```

### 2. Manus 代理清理优化

**文件**: `app/agent/manus.py`

**主要改进**:
- 使用 `asyncio.shield()` 保护关键清理操作
- 增强异常处理和日志记录
- 确保清理过程的原子性

```python
async def cleanup(self):
    """Clean up Manus agent resources."""
    try:
        # 清理浏览器上下文
        if self.browser_context_helper:
            try:
                await asyncio.shield(self.browser_context_helper.cleanup_browser())
            except Exception as e:
                logger.warning(f"Error cleaning up browser context: {e}")
        
        # 断开 MCP 服务器连接（仅在已初始化时）
        if self._initialized:
            try:
                await asyncio.shield(self.disconnect_mcp_server())
            except Exception as e:
                logger.warning(f"Error disconnecting MCP servers: {e}")
            finally:
                self._initialized = False
                
        logger.info("Manus cleanup completed")
    except Exception as e:
        logger.error(f"Error during Manus cleanup: {e}")
```

### 3. FastAPI 生命周期管理改进

**文件**: `web_api/main.py`

**主要改进**:
- 添加超时机制防止无限等待
- 增强错误处理和日志记录
- 使用 `asyncio.wait_for()` 设置清理超时

```python
@asynccontextmanager
async def lifespan(app: FastAPI):
    """应用生命周期管理"""
    # 启动时执行
    print("🚀 启动后台任务...")
    try:
        await start_background_tasks()
        print("✅ 后台任务启动完成")
    except Exception as e:
        print(f"⚠️ 后台任务启动失败: {e}")

    yield

    # 关闭时执行
    print("🧹 清理资源...")
    try:
        from .services.background import get_global_manus_instance
        global_manus = get_global_manus_instance()
        if global_manus:
            try:
                # 使用 asyncio.wait_for 设置超时，避免无限等待
                await asyncio.wait_for(global_manus.cleanup(), timeout=10.0)
                print("✅ Manus 实例清理完成")
            except asyncio.TimeoutError:
                print("⚠️ Manus 清理超时，强制退出")
            except Exception as e:
                print(f"⚠️ Manus 清理时出错: {e}")
    except Exception as e:
        print(f"⚠️ 资源清理过程中出错: {e}")
    finally:
        print("✅ 清理完成")
```

### 4. ToolCallAgent 清理优化

**文件**: `app/agent/toolcall.py`

**主要改进**:
- 实现并行工具清理
- 使用 `asyncio.shield()` 保护清理过程
- 增强错误处理和日志记录

```python
async def cleanup(self):
    """Clean up resources used by the agent's tools."""
    logger.info(f"🧹 Cleaning up resources for agent '{self.name}'...")
    
    # 收集所有清理任务
    cleanup_tasks = []
    for tool_name, tool_instance in self.available_tools.tool_map.items():
        if hasattr(tool_instance, "cleanup") and asyncio.iscoroutinefunction(tool_instance.cleanup):
            logger.debug(f"🧼 Preparing cleanup for tool: {tool_name}")
            task = asyncio.create_task(self._safe_cleanup_tool(tool_name, tool_instance))
            cleanup_tasks.append(task)
    
    # 并行执行所有清理任务，忽略异常
    if cleanup_tasks:
        results = await asyncio.gather(*cleanup_tasks, return_exceptions=True)
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                tool_name = list(self.available_tools.tool_map.keys())[i]
                logger.warning(f"Tool cleanup failed for {tool_name}: {result}")
    
    logger.info(f"✨ Cleanup complete for agent '{self.name}'.")

async def _safe_cleanup_tool(self, tool_name: str, tool_instance):
    """安全地清理单个工具"""
    try:
        await asyncio.shield(tool_instance.cleanup())
        logger.debug(f"✅ Tool cleanup successful: {tool_name}")
    except Exception as e:
        logger.error(f"🚨 Error cleaning up tool '{tool_name}': {e}")
        raise
```

## 修复效果

### 预期改进

1. **消除 Cancel Scope 错误**：
   - 使用 `asyncio.shield()` 保护关键清理操作
   - 正确处理 `CancelledError` 和 cancel scope 相关异常
   - 减少错误日志的产生

2. **提高清理过程的健壮性**：
   - 添加超时机制防止无限等待
   - 实现并行清理提高效率
   - 增强异常处理确保清理完整性

3. **改善系统可维护性**：
   - 减少误导性错误日志
   - 提供更清晰的清理状态反馈
   - 确保资源正确释放

### 测试验证

1. **功能测试**：
   - 启动 Web API 服务
   - 执行包含 terminate 工具的任务
   - 验证任务正常完成且无错误日志

2. **压力测试**：
   - 连续执行多个任务
   - 验证资源清理的稳定性
   - 确认内存和连接不会泄漏

3. **异常场景测试**：
   - 模拟网络中断等异常情况
   - 验证清理过程的容错能力
   - 确认系统能够优雅降级

## 注意事项

1. **向后兼容性**：所有修改都保持了原有 API 的兼容性
2. **性能影响**：并行清理可能会提高清理效率，但需要监控资源使用
3. **日志级别**：部分错误降级为警告，避免误导性的错误日志
4. **超时设置**：清理超时设置为 10 秒，可根据实际情况调整

## 总结

通过这次修复，我们解决了 terminate 工具调用后产生的 cancel scope 错误问题。主要通过以下手段：

1. 使用 `asyncio.shield()` 保护关键清理操作不被取消
2. 增强异常处理，特别是针对 cancel scope 相关错误
3. 实现并行清理和超时机制提高健壮性
4. 改善日志记录减少误导性错误信息

这些改进不仅解决了当前的错误问题，还提高了整个系统的稳定性和可维护性。
