"""
Agent 详细信息路由模块

提供获取Agent执行详情的API端点
"""
from fastapi import APIRouter, HTTPException, Depends
from typing import List, Optional

from ..schemas.agent import AgentExecutionDetail, AgentLogEntry
from ..schemas.task import TaskResponse
from ..services.state_manager import state_manager
from ..services.agent_adapter import SimpleAgentAdapter

# 创建路由器
router = APIRouter(
    prefix="/agent-details",
    tags=["agent-details"],
    responses={404: {"description": "Not found"}},
)

# 依赖注入：获取agent适配器实例
async def get_agent_adapter():
    # 这里应该从应用状态中获取adapter实例
    # 目前创建一个新实例作为示例
    return SimpleAgentAdapter(state_manager)


@router.get("/tasks/{task_id}/execution", response_model=Optional[AgentExecutionDetail])
async def get_task_execution_detail(
    task_id: str,
    adapter: SimpleAgentAdapter = Depends(get_agent_adapter)
):
    """
    获取任务的详细执行信息
    
    包括：
    - 执行步骤详情
    - 思考过程
    - 工具调用记录
    - 执行时间统计
    """
    try:
        detail = await adapter.get_task_execution_detail(task_id)
        if not detail:
            # 如果没有详细信息，返回基本任务信息
            task_info = state_manager.get_task(task_id)
            if not task_info:
                raise HTTPException(status_code=404, detail=f"任务 {task_id} 不存在")
            
            # 返回None表示没有详细执行信息
            return None
        
        return detail
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取任务执行详情失败: {str(e)}")


@router.get("/tasks/{task_id}/logs", response_model=List[AgentLogEntry])
async def get_task_logs(
    task_id: str,
    adapter: SimpleAgentAdapter = Depends(get_agent_adapter)
):
    """
    获取任务的执行日志
    
    包括：
    - Agent日志
    - 工具调用日志
    - 系统日志
    """
    try:
        # 检查任务是否存在
        task_info = state_manager.get_task(task_id)
        if not task_info:
            raise HTTPException(status_code=404, detail=f"任务 {task_id} 不存在")
        
        logs = await adapter.get_task_logs(task_id)
        return logs
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取任务日志失败: {str(e)}")


@router.get("/tasks/{task_id}/steps")
async def get_task_steps(task_id: str):
    """
    获取任务的执行步骤列表
    
    返回每个步骤的基本信息和状态
    """
    try:
        # 检查任务是否存在
        task_info = state_manager.get_task(task_id)
        if not task_info:
            raise HTTPException(status_code=404, detail=f"任务 {task_id} 不存在")
        
        # 从adapter获取详细信息
        adapter = SimpleAgentAdapter(state_manager)
        detail = await adapter.get_task_execution_detail(task_id)
        
        if detail and detail.steps:
            steps_summary = []
            for step in detail.steps:
                steps_summary.append({
                    "step": step.step,
                    "action": step.action,
                    "status": step.status,
                    "timestamp": step.timestamp.isoformat(),
                    "duration": step.duration,
                    "tool_calls_count": len(step.tool_calls),
                    "observations_count": len(step.observations),
                    "has_error": step.error is not None
                })
            return {
                "task_id": task_id,
                "total_steps": len(steps_summary),
                "steps": steps_summary
            }
        else:
            # 返回基本步骤信息
            return {
                "task_id": task_id,
                "total_steps": task_info.get("current_step", 0),
                "steps": [],
                "message": "详细步骤信息不可用"
            }
    
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取任务步骤失败: {str(e)}")


@router.get("/tasks/{task_id}/tools")
async def get_task_tool_calls(task_id: str):
    """
    获取任务中所有工具调用的汇总信息
    """
    try:
        # 检查任务是否存在
        task_info = state_manager.get_task(task_id)
        if not task_info:
            raise HTTPException(status_code=404, detail=f"任务 {task_id} 不存在")
        
        # 从adapter获取详细信息
        adapter = SimpleAgentAdapter(state_manager)
        detail = await adapter.get_task_execution_detail(task_id)
        
        if detail and detail.steps:
            all_tool_calls = []
            tool_stats = {}
            
            for step in detail.steps:
                for tool_call in step.tool_calls:
                    all_tool_calls.append({
                        "step": step.step,
                        "tool_name": tool_call.tool_name,
                        "tool_id": tool_call.tool_id,
                        "status": tool_call.status,
                        "timestamp": tool_call.timestamp.isoformat(),
                        "execution_time": tool_call.execution_time,
                        "has_error": tool_call.error is not None
                    })
                    
                    # 统计工具使用情况
                    if tool_call.tool_name not in tool_stats:
                        tool_stats[tool_call.tool_name] = {
                            "count": 0,
                            "success_count": 0,
                            "error_count": 0,
                            "total_time": 0.0
                        }
                    
                    tool_stats[tool_call.tool_name]["count"] += 1
                    if tool_call.status == "success":
                        tool_stats[tool_call.tool_name]["success_count"] += 1
                    elif tool_call.status == "error":
                        tool_stats[tool_call.tool_name]["error_count"] += 1
                    
                    if tool_call.execution_time:
                        tool_stats[tool_call.tool_name]["total_time"] += tool_call.execution_time
            
            return {
                "task_id": task_id,
                "total_tool_calls": len(all_tool_calls),
                "tool_calls": all_tool_calls,
                "tool_statistics": tool_stats
            }
        else:
            return {
                "task_id": task_id,
                "total_tool_calls": 0,
                "tool_calls": [],
                "tool_statistics": {},
                "message": "工具调用信息不可用"
            }
    
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取工具调用信息失败: {str(e)}")


@router.get("/sessions/{session_id}/summary")
async def get_session_execution_summary(session_id: str):
    """
    获取会话的执行汇总信息
    
    包括所有任务的执行统计
    """
    try:
        # 检查会话是否存在
        session_info = state_manager.get_session(session_id)
        if not session_info:
            raise HTTPException(status_code=404, detail=f"会话 {session_id} 不存在")
        
        # 获取会话的所有任务
        tasks = state_manager.get_session_tasks(session_id)
        
        summary = {
            "session_id": session_id,
            "total_tasks": len(tasks),
            "completed_tasks": 0,
            "failed_tasks": 0,
            "running_tasks": 0,
            "cancelled_tasks": 0,
            "total_execution_time": 0.0,
            "tasks_summary": []
        }
        
        for task in tasks:
            task_summary = {
                "task_id": task["task_id"],
                "status": task["status"],
                "current_step": task.get("current_step", 0),
                "created_at": task["created_at"].isoformat() if task.get("created_at") else None,
                "start_time": task["start_time"].isoformat() if task.get("start_time") else None,
                "end_time": task["end_time"].isoformat() if task.get("end_time") else None,
                "has_result": task.get("result") is not None,
                "has_error": task.get("error_message") is not None
            }
            
            # 计算执行时间
            if task.get("start_time") and task.get("end_time"):
                duration = (task["end_time"] - task["start_time"]).total_seconds()
                task_summary["duration"] = duration
                summary["total_execution_time"] += duration
            
            # 统计任务状态
            status = task["status"]
            if status == "completed":
                summary["completed_tasks"] += 1
            elif status == "error":
                summary["failed_tasks"] += 1
            elif status == "running":
                summary["running_tasks"] += 1
            elif status == "cancelled":
                summary["cancelled_tasks"] += 1
            
            summary["tasks_summary"].append(task_summary)
        
        return summary
    
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取会话汇总失败: {str(e)}")
