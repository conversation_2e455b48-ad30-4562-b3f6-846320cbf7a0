# OpenManus RPA Agent Project Overview

OpenManus is a versatile AI agent framework that can solve various tasks using multiple tools, including MCP (Model Context Protocol) integration, browser automation, and various analysis capabilities.

## Project Architecture

### Core Components
- **Main Entry Point**: [main.py](mdc:main.py) - CLI entry point that initializes and runs the Manus agent
- **Configuration**: [config/config.toml](mdc:config/config.toml) - Global configuration including LLM settings, browser, search, and MCP settings
- **Agent Core**: [app/agent/manus.py](mdc:app/agent/manus.py) - The main Manus agent class that orchestrates all functionality

### Key Directories
- `app/` - Main application code
  - `agent/` - Agent implementations (Manus, browser, toolcall, etc.)
  - `tool/` - Available tools (browser automation, file operations, Python execution, etc.)
  - `config.py` - Configuration management
  - `llm.py` - LLM integration and communication
  - `mcp/` - Model Context Protocol implementation
- `config/` - Configuration files
- `workspace/` - Agent working directory
- `examples/` - Usage examples
- `tests/` - Test suite

### Key Features
1. **Multi-LLM Support**: Supports various LLM providers (OpenAI, Anthropic, Google Gemini, Azure, Ollama, etc.)
2. **MCP Integration**: Model Context Protocol for extensible tool integration
3. **Browser Automation**: Playwright-based web automation capabilities
4. **Tool Ecosystem**: Comprehensive tool collection for various tasks
5. **Configuration Management**: TOML-based configuration with environment support

## Development Guidelines
- Use Python 3.12+ (project requires 3.11-3.13)
- Follow async/await patterns for agent operations
- Use Pydantic models for data validation
- Configuration should be managed through [config/config.toml](mdc:config/config.toml)
- All agents inherit from base agent classes in `app/agent/base.py`
description:
globs:
alwaysApply: false
---
