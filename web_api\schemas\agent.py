"""
Agent 相关的数据模型
"""
from pydantic import BaseModel, Field
from typing import Dict, List, Optional, Any, Union
from enum import Enum
from datetime import datetime


class AgentType(str, Enum):
    """Agent 类型枚举"""
    MANUS = "manus"
    BROWSER = "browser"
    SWE = "swe"
    MCP = "mcp"


class AgentInfo(BaseModel):
    """Agent 信息模型"""
    agent_type: AgentType = Field(..., description="Agent 类型")
    capabilities: List[str] = Field(default_factory=list, description="Agent 能力列表")
    description: Optional[str] = Field(default=None, description="Agent 描述")


class AgentRunRequest(BaseModel):
    """运行 Agent 的请求模型"""
    agent_type: AgentType = Field(..., description="Agent 类型")
    instruction: str = Field(..., description="指令")
    parameters: Optional[Dict[str, Any]] = Field(default=None, description="参数")


# 新增：Agent执行过程的详细数据模型

class ThinkingProcess(BaseModel):
    """Agent思考过程模型"""
    step: int = Field(..., description="思考步骤")
    timestamp: datetime = Field(..., description="时间戳")
    content: str = Field(..., description="思考内容")
    reasoning: Optional[str] = Field(None, description="推理过程")
    decision: Optional[str] = Field(None, description="决策结果")


class ToolCallInfo(BaseModel):
    """工具调用信息模型"""
    tool_name: str = Field(..., description="工具名称")
    tool_id: str = Field(..., description="工具调用ID")
    arguments: Dict[str, Any] = Field(..., description="工具参数")
    timestamp: datetime = Field(..., description="调用时间")
    status: str = Field(..., description="调用状态: pending|running|success|error")
    result: Optional[str] = Field(None, description="工具执行结果")
    error: Optional[str] = Field(None, description="错误信息")
    execution_time: Optional[float] = Field(None, description="执行时间(秒)")


class ExecutionStep(BaseModel):
    """执行步骤详细信息模型"""
    step: int = Field(..., description="步骤编号")
    timestamp: datetime = Field(..., description="步骤开始时间")
    action: str = Field(..., description="执行动作")
    status: str = Field(..., description="步骤状态: pending|running|completed|error")
    thinking: Optional[ThinkingProcess] = Field(None, description="思考过程")
    tool_calls: List[ToolCallInfo] = Field(default_factory=list, description="工具调用列表")
    observations: List[str] = Field(default_factory=list, description="观察结果")
    duration: Optional[float] = Field(None, description="步骤执行时间(秒)")
    error: Optional[str] = Field(None, description="错误信息")


class AgentExecutionDetail(BaseModel):
    """Agent执行详情模型"""
    task_id: str = Field(..., description="任务ID")
    agent_type: AgentType = Field(..., description="Agent类型")
    instruction: str = Field(..., description="原始指令")
    status: str = Field(..., description="执行状态")
    current_step: int = Field(default=0, description="当前步骤")
    max_steps: int = Field(default=20, description="最大步骤数")
    steps: List[ExecutionStep] = Field(default_factory=list, description="执行步骤列表")
    start_time: Optional[datetime] = Field(None, description="开始时间")
    end_time: Optional[datetime] = Field(None, description="结束时间")
    total_duration: Optional[float] = Field(None, description="总执行时间(秒)")
    final_result: Optional[str] = Field(None, description="最终结果")
    error_message: Optional[str] = Field(None, description="错误信息")


class AgentLogEntry(BaseModel):
    """Agent日志条目模型"""
    timestamp: datetime = Field(..., description="时间戳")
    level: str = Field(..., description="日志级别: DEBUG|INFO|WARNING|ERROR")
    component: str = Field(..., description="组件名称: agent|tool|system")
    message: str = Field(..., description="日志消息")
    context: Optional[Dict[str, Any]] = Field(None, description="上下文信息")


class AgentMemoryState(BaseModel):
    """Agent记忆状态模型"""
    messages_count: int = Field(..., description="消息数量")
    last_message: Optional[str] = Field(None, description="最后一条消息")
    context_length: int = Field(..., description="上下文长度")
    memory_usage: Optional[Dict[str, Any]] = Field(None, description="内存使用情况")
