# Development Workflow Guide

## Project Structure Standards

- **Entry Points**: Multiple entry points for different use cases
  - [main.py](mdc:main.py) - CLI interface
  - [run_mcp.py](mdc:run_mcp.py) - MCP client mode
  - [run_with_playwright_mcp.py](mdc:run_with_playwright_mcp.py) - Playwright MCP mode
  - [run_flow.py](mdc:run_flow.py) - Workflow execution
  - [run_web_api.py](mdc:run_web_api.py) - Web API server

## Dependencies & Requirements

- **Core Dependencies**: [requirements.txt](mdc:requirements.txt) - Main project dependencies
- **Setup**: [setup.py](mdc:setup.py) - Package configuration
- **Python Version**: 3.12+ (supports 3.11-3.13)

## Development Environment Setup

1. Clone repository
2. Create virtual environment: `python -m venv .venv`
3. Activate: `.venv\Scripts\activate` (Windows) or `source .venv/bin/activate` (Unix)
4. Install dependencies: `pip install -r requirements.txt`
5. Configure [config/config.toml](mdc:config/config.toml) with your LLM settings

## Code Quality & Standards

- **Pre-commit**: [.pre-commit-config.yaml](mdc:.pre-commit-config.yaml) - Code quality hooks
- **Git Attributes**: [.gitattributes](mdc:.gitattributes) - Git configuration
- **Ignore Rules**: [.gitignore](mdc:.gitignore) - Git ignore patterns

## Testing

- **Test Directory**: [tests/](mdc:tests/) - Test suite
- **Examples**: [examples/](mdc:examples/) - Usage examples
- Run tests with appropriate Python testing framework

## Docker Support

- **Dockerfile**: [Dockerfile](mdc:Dockerfile) - Container configuration
- Supports containerized deployment

## Documentation

- **Main README**: [README.md](mdc:README.md) - English documentation
- **Internationalization**:
  - [README_zh.md](mdc:README_zh.md) - Chinese
  - [README_ko.md](mdc:README_ko.md) - Korean
  - [README_ja.md](mdc:README_ja.md) - Japanese
- **Integration Docs**: Various MCP and Playwright integration guides

## Workspace Organization

- **workspace/**: Agent working directory - keep this clean
- **logs/**: Application logs
- **mds/**: Markdown documents
- **assets/**: Static assets (logos, images)
- **external/**: External dependencies or resources

## Best Practices

- Use type hints throughout the codebase
- Follow async/await patterns for I/O operations
- Implement proper error handling and logging
- Use Pydantic models for data validation
- Keep configuration in TOML files
- Document public APIs and complex logic
- Test agent interactions thoroughly
- Clean up resources in agent cleanup methods

## Contribution Guidelines

- Follow existing code style and patterns
- Add tests for new functionality
- Update documentation for significant changes
- Use meaningful commit messages
- Test across different Python versions (3.11-3.13)
  description:
  globs:
  alwaysApply: false

---
