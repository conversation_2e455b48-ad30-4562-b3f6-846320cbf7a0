/**
 * 简单测试CDP修复
 */

const { contextFactory } = require('./external/playwright-mcp/lib/browserContextFactory.js');

async function testCDPFix() {
    console.log('🧪 测试CDP修复...');
    
    const browserConfig = {
        browserName: 'chromium',
        cdpEndpoint: 'auto',
        launchOptions: {
            channel: 'chrome',
            headless: false,
            chromiumSandbox: false
        },
        contextOptions: {},
        isolated: false
    };
    
    try {
        console.log('📋 创建CDP上下文工厂...');
        const factory = contextFactory(browserConfig);
        console.log(`✅ 工厂类型: ${factory.name}`);
        
        console.log('📋 尝试获取浏览器实例...');
        const { browserContext, close } = await factory.createContext();
        console.log('✅ 成功创建浏览器上下文!');
        
        // 测试基本功能
        console.log('📋 测试基本功能...');
        const page = await browserContext.newPage();
        await page.goto('https://www.bing.com');
        console.log('✅ 成功打开页面!');
        
        // 清理
        await page.close();
        await close();
        console.log('✅ 清理完成!');
        
        console.log('\n🎉 CDP修复测试成功!');
        
    } catch (error) {
        console.error('❌ CDP修复测试失败:', error.message);
        console.error('详细错误:', error);
    }
}

testCDPFix().catch(console.error);
