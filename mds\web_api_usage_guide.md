# RPA Agent Web API 使用指南

## 🌐 服务信息

- **基础 URL**: `http://127.0.0.1:8000`
- **API 文档**: `http://127.0.0.1:8000/docs`
- **WebSocket**: `ws://127.0.0.1:8000/ws/sessions/{session_id}`

## 🚀 快速开始：让 Agent 执行 Python 数组去重任务

### 步骤 1: 创建会话

**请求**:

```http
POST /api/sessions
Content-Type: application/json
```

**cURL 示例**:

```bash
curl -X POST "http://127.0.0.1:8000/api/sessions"
```

**Postman 设置**:

- Method: `POST`
- URL: `http://127.0.0.1:8000/api/sessions`
- Headers: `Content-Type: application/json`
- Body: 留空（简化版不需要额外参数）

**响应示例**:

```json
{
  "session_id": "550e8400-e29b-41d4-a716-446655440000",
  "status": "active",
  "created_at": "2025-01-16T12:30:00.000Z",
  "task_count": 0
}
```

### 步骤 2: 创建任务（Python 数组去重）

**请求**:

```http
POST /api/sessions/{session_id}/tasks
Content-Type: application/json

{
  "message": "请帮我编写一个Python函数来实现数组去重，要求保持原有顺序，并提供多种实现方法的对比"
}
```

**Postman 设置**:

- Method: `POST`
- URL: `http://127.0.0.1:8000/api/sessions/550e8400-e29b-41d4-a716-446655440000/tasks`
- Headers: `Content-Type: application/json`
- Body (raw JSON):

```json
{
  "message": "请帮我编写一个Python函数来实现数组去重，要求保持原有顺序，并提供多种实现方法的对比"
}
```

**响应示例**:

```json
{
  "task_id": "123e4567-e89b-12d3-a456-426614174000",
  "session_id": "550e8400-e29b-41d4-a716-446655440000",
  "message": "请帮我编写一个Python函数来实现数组去重，要求保持原有顺序，并提供多种实现方法的对比",
  "status": "queued",
  "current_step": 0,
  "max_steps": 20,
  "created_at": "2025-01-16T12:31:00.000Z",
  "start_time": null,
  "end_time": null,
  "result": null,
  "error_message": null
}
```

### 步骤 3: 监控任务状态

**请求**:

```http
GET /api/sessions/{session_id}/tasks/{task_id}
```

**Postman 设置**:

- Method: `GET`
- URL: `http://127.0.0.1:8000/api/sessions/550e8400-e29b-41d4-a716-446655440000/tasks/123e4567-e89b-12d3-a456-426614174000`

**状态变化过程**:

1. `queued` → 任务已加入队列
2. `running` → 任务正在执行
3. `completed` → 任务执行完成
4. `error` → 任务执行出错

## 📋 完整 API 参考

### 1. 会话管理

#### 创建会话

```http
POST /api/sessions
```

#### 获取会话信息

```http
GET /api/sessions/{session_id}
```

#### 获取所有会话

```http
GET /api/sessions
```

### 2. 任务管理

#### 创建任务

```http
POST /api/sessions/{session_id}/tasks
Content-Type: application/json

{
  "message": "你的任务描述"
}
```

#### 获取任务状态

```http
GET /api/sessions/{session_id}/tasks/{task_id}
```

#### 获取会话所有任务

```http
GET /api/sessions/{session_id}/tasks
```

#### 取消任务

```http
POST /api/sessions/{session_id}/tasks/{task_id}/cancel
```

### 3. 工具信息

#### 获取所有可用工具

```http
GET /api/tools
```

#### 获取工具分类

```http
GET /api/tools/categories
```

### 4. 健康检查

```http
GET /api/health
```

## 🎯 更多任务示例

### 示例 1: 文件操作任务

```json
{
  "message": "请帮我创建一个Python脚本，读取CSV文件并生成数据统计报告"
}
```

### 示例 2: 数据处理任务

```json
{
  "message": "请帮我编写一个函数，从给定的JSON数据中提取特定字段并进行数据清洗"
}
```

### 示例 3: 算法实现任务

```json
{
  "message": "请实现一个快速排序算法，并添加详细注释说明每个步骤"
}
```

### 示例 4: 爬虫任务

```json
{
  "message": "请帮我编写一个简单的网页爬虫，获取指定网站的标题和链接"
}
```

### 示例 5: 数据可视化任务

```json
{
  "message": "请生成一个包含随机数据的柱状图，使用matplotlib库"
}
```

## 🔄 WebSocket 实时监控

### 连接 WebSocket

```javascript
const ws = new WebSocket("ws://127.0.0.1:8000/ws/sessions/YOUR_SESSION_ID");

ws.onopen = function (event) {
  console.log("WebSocket连接已建立");
};

ws.onmessage = function (event) {
  const data = JSON.parse(event.data);
  console.log("收到消息:", data);

  if (data.type === "task_status") {
    console.log("任务状态更新:", data.data);
  } else if (data.type === "step_update") {
    console.log("执行步骤:", data.data);
  }
};

// 发送心跳
ws.send(JSON.stringify({ type: "ping" }));

// 获取状态
ws.send(JSON.stringify({ type: "get_status" }));
```

### WebSocket 消息类型

1. **连接消息**:

```json
{
  "type": "connection",
  "timestamp": "2025-01-16T12:30:00.000Z",
  "data": {
    "status": "connected",
    "session_id": "...",
    "message": "WebSocket 连接已建立"
  }
}
```

2. **任务状态更新**:

```json
{
  "type": "task_status",
  "timestamp": "2025-01-16T12:31:00.000Z",
  "data": {
    "task_id": "...",
    "status": "running",
    "current_step": 1,
    "message": "开始执行任务"
  }
}
```

3. **步骤更新**:

```json
{
  "type": "step_update",
  "timestamp": "2025-01-16T12:31:30.000Z",
  "data": {
    "task_id": "...",
    "step": 2,
    "action": "执行Python代码",
    "thoughts": "正在生成数组去重函数..."
  }
}
```

## 📊 Postman Collection 配置

### 环境变量设置

在 Postman 中创建环境变量：

- `base_url`: `http://127.0.0.1:8000`
- `session_id`: `{{session_id}}` (从创建会话响应中获取)
- `task_id`: `{{task_id}}` (从创建任务响应中获取)

### 自动化脚本

在 Postman 的 Tests 标签中添加以下脚本来自动保存响应数据：

**创建会话的 Tests 脚本**:

```javascript
if (pm.response.code === 201) {
  const response = pm.response.json();
  pm.environment.set("session_id", response.session_id);
  console.log("Session ID saved:", response.session_id);
}
```

**创建任务的 Tests 脚本**:

```javascript
if (pm.response.code === 201) {
  const response = pm.response.json();
  pm.environment.set("task_id", response.task_id);
  console.log("Task ID saved:", response.task_id);
}
```

## 🔍 调试提示

### 1. 查看服务日志

在运行`python run_web_api.py`的终端中查看实时日志

### 2. 检查服务状态

```http
GET /api/health
```

### 3. 常见状态码

- `200`: 请求成功
- `201`: 创建成功
- `404`: 资源不存在
- `500`: 服务器内部错误

### 4. 错误处理

如果任务执行失败，检查任务的`error_message`字段：

```json
{
  "status": "error",
  "error_message": "具体错误信息"
}
```

## 🎉 开始测试

现在你可以：

1. 打开 Postman
2. 导入上述 API 配置
3. 按照步骤 1-3 的顺序测试
4. 观察任务执行过程和结果

**推荐测试顺序**:

1. 健康检查 → 创建会话 → 创建简单任务 → 监控任务状态
2. 尝试不同类型的任务
3. 测试 WebSocket 实时通信
4. 测试任务取消功能

祝测试愉快！🚀
